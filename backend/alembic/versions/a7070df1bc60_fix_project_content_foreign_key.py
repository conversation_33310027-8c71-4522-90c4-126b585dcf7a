"""fix_project_content_foreign_key

Revision ID: a7070df1bc60
Revises: add_project_content
Create Date: 2025-08-04 16:10:26.100550

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a7070df1bc60'
down_revision = 'add_project_content'
branch_labels = None
depends_on = None


def upgrade():
    # Use batch mode for SQLite compatibility
    with op.batch_alter_table('project_contents', schema=None) as batch_op:
        # Change project_id column type from Integer to String(64)
        batch_op.alter_column('project_id',
                             existing_type=sa.Integer(),
                             type_=sa.String(64),
                             existing_nullable=True)


def downgrade():
    # Use batch mode for SQLite compatibility
    with op.batch_alter_table('project_contents', schema=None) as batch_op:
        # Change project_id column type back to Integer
        batch_op.alter_column('project_id',
                             existing_type=sa.String(64),
                             type_=sa.Integer(),
                             existing_nullable=True)
