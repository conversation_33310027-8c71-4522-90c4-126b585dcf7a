"""
Contextual SEO Analyzer
Provides deep, context-aware analysis of user content with specific, actionable suggestions
"""

import logging
import re
from datetime import datetime
from typing import Dict, Any, List

try:
    from app.services.ai_provider_service import AIProviderService
    AI_PROVIDER_AVAILABLE = True
except ImportError as e:
    logger.warning(f"⚠️ AIProviderService not available: {e}")
    AI_PROVIDER_AVAILABLE = False
    AIProviderService = None

logger = logging.getLogger(__name__)

class ContextualSEOAnalyzer:
    """Advanced SEO analyzer that provides context-aware, actionable suggestions."""
    
    def __init__(self):
        if AI_PROVIDER_AVAILABLE:
            self.ai_provider = AIProviderService()
            logger.info("✅ Contextual SEO Analyzer initialized successfully with AI provider")
        else:
            self.ai_provider = None
            logger.info("✅ Contextual SEO Analyzer initialized successfully (AI provider unavailable)")
    
    async def analyze_content_deeply(self, content: str, target_keywords: List[str] = None) -> Dict[str, Any]:
        """
        Perform deep contextual analysis of content.
        
        Args:
            content: The user's content to analyze
            target_keywords: Keywords the user wants to target
            
        Returns:
            Comprehensive analysis with specific, actionable suggestions
        """
        try:
            # Analyze content structure and characteristics
            content_analysis = self._analyze_content_structure(content)
            
            # Identify specific weaknesses and gaps
            weaknesses = self._identify_content_weaknesses(content, content_analysis, target_keywords)
            
            # Generate context-specific suggestions
            suggestions = await self._generate_contextual_suggestions(
                content, content_analysis, weaknesses, target_keywords
            )
            
            # Calculate contextual scores
            scores = self._calculate_contextual_scores(content_analysis, weaknesses)

            return {
                'content_analysis': content_analysis,
                'weaknesses': weaknesses,
                'suggestions': suggestions,
                'scores': scores,
                'analysis_type': 'deep_contextual'
            }
            
        except Exception as e:
            logger.error(f"❌ Deep content analysis failed: {str(e)}")
            return self._get_fallback_analysis(content)

    def analyze_content_confidence(self, content: str, target_keywords: List[str] = None) -> Dict[str, Any]:
        """
        Analyze content confidence for LLM ranking (Perplexity, ChatGPT, Claude, etc.)
        Triggered manually by user button click - evaluates LLM-specific ranking factors.
        """
        try:
            logger.info("🧠 Performing LLM confidence analysis (user-triggered)")

            # Get basic content analysis
            content_analysis = self._analyze_content_structure(content)

            # Identify LLM-specific weaknesses
            weaknesses = self._identify_content_weaknesses(content, content_analysis, target_keywords)

            # Add LLM confidence scoring to each weakness
            for weakness in weaknesses:
                weakness.update(self._calculate_weakness_confidence(weakness, content_analysis))

            # Calculate overall LLM confidence
            overall_confidence = self._calculate_overall_content_confidence(weaknesses, content_analysis)

            return {
                'confidence_analysis': {
                    'overall_confidence': overall_confidence,
                    'weaknesses_with_confidence': weaknesses,
                    'content_stats': content_analysis,
                    'analysis_timestamp': datetime.utcnow().isoformat(),
                    'trigger_type': 'manual_llm_analysis',
                    'analysis_type': 'llm_ranking_confidence'
                },
                'status': 'success'
            }

        except Exception as e:
            logger.error(f"❌ Confidence analysis failed: {str(e)}")
            return {
                'confidence_analysis': {
                    'error': str(e),
                    'overall_confidence': {
                        'overall_score': 50.0,
                        'confidence_badge': 'Mejorable',
                        'confidence_color': '#ef4444',
                        'quality_level': 'Error en análisis'
                    }
                },
                'status': 'error'
            }

    def _analyze_content_structure(self, content: str) -> Dict[str, Any]:
        """Analyze the structure and characteristics of the content."""
        words = content.split()
        sentences = [s.strip() for s in re.split(r'[.!?]+', content) if s.strip()]
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        # Extract headings (simple detection)
        headings = re.findall(r'^#+\s+(.+)$', content, re.MULTILINE)
        
        # Detect lists
        list_items = re.findall(r'^\s*[-*•]\s+(.+)$', content, re.MULTILINE)
        numbered_lists = re.findall(r'^\s*\d+\.\s+(.+)$', content, re.MULTILINE)
        
        # Analyze sentence complexity
        avg_words_per_sentence = len(words) / max(len(sentences), 1)
        long_sentences = [s for s in sentences if len(s.split()) > 25]
        
        # Detect questions
        questions = [s for s in sentences if s.strip().endswith('?')]
        
        # Analyze keyword density
        word_freq = {}
        for word in words:
            clean_word = re.sub(r'[^\w]', '', word.lower())
            if len(clean_word) > 3:
                word_freq[clean_word] = word_freq.get(clean_word, 0) + 1
        
        # Detect technical terms (words > 8 characters)
        technical_terms = [w for w in words if len(w) > 8 and w.isalpha()]
        
        return {
            'word_count': len(words),
            'sentence_count': len(sentences),
            'paragraph_count': len(paragraphs),
            'heading_count': len(headings),
            'list_items': len(list_items) + len(numbered_lists),
            'avg_words_per_sentence': avg_words_per_sentence,
            'long_sentences_count': len(long_sentences),
            'questions_count': len(questions),
            'technical_terms_count': len(technical_terms),
            'word_frequency': dict(sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]),
            'has_headings': len(headings) > 0,
            'has_lists': len(list_items) + len(numbered_lists) > 0,
            'has_questions': len(questions) > 0,
            'readability_estimate': self._estimate_readability(avg_words_per_sentence, len(long_sentences))
        }
    
    def _identify_content_weaknesses(self, content: str, analysis: Dict[str, Any], target_keywords: List[str] = None) -> List[Dict[str, Any]]:
        """Identify specific weaknesses in the content with confidence scoring."""
        weaknesses = []

        # Length issues
        if analysis['word_count'] < 150:
            weaknesses.append({
                'type': 'length',
                'severity': 'high',
                'issue': 'Content too short',
                'current_value': analysis['word_count'],
                'target_value': '300-500 words',
                'impact': 'Low SEO ranking potential'
            })

        # Structure issues
        if analysis['paragraph_count'] < 2 and analysis['word_count'] > 100:
            weaknesses.append({
                'type': 'structure',
                'severity': 'medium',
                'issue': 'Poor paragraph structure',
                'current_value': analysis['paragraph_count'],
                'target_value': '3-5 paragraphs',
                'impact': 'Reduced readability'
            })

        # Readability issues
        if analysis['avg_words_per_sentence'] > 20:
            weaknesses.append({
                'type': 'readability',
                'severity': 'medium',
                'issue': 'Oraciones muy largas - difíciles de leer',
                'current_value': f"{analysis['avg_words_per_sentence']:.1f} palabras por oración",
                'target_value': '15-20 palabras por oración',
                'impact': 'Reduce la comprensión y engagement'
            })

        # Engagement issues
        if not analysis['has_questions'] and analysis['word_count'] > 100:
            weaknesses.append({
                'type': 'engagement',
                'severity': 'low',
                'issue': 'Sin preguntas - falta interacción con el lector',
                'current_value': '0 preguntas',
                'target_value': '1-2 preguntas para engagement',
                'impact': 'Menor tiempo en página y engagement'
            })

        # SEO structure issues
        if not analysis['has_headings'] and analysis['word_count'] > 200:
            weaknesses.append({
                'type': 'seo_structure',
                'severity': 'high',
                'issue': 'Sin títulos - falta estructura clara',
                'current_value': '0 encabezados',
                'target_value': '2-4 títulos (H2, H3)',
                'impact': 'Google y LLMs no entienden la estructura'
            })

        if not analysis['has_lists'] and analysis['word_count'] > 150:
            weaknesses.append({
                'type': 'seo_structure',
                'severity': 'medium',
                'issue': 'Sin listas - información poco organizada',
                'current_value': '0 listas',
                'target_value': '1-2 listas con viñetas',
                'impact': 'Contenido menos escaneable y atractivo'
            })

        # Check for keyword optimization issues
        if target_keywords:
            keyword_issues = self._analyze_keyword_usage(content, target_keywords)
            weaknesses.extend(keyword_issues)

        # Check for semantic SEO opportunities
        semantic_issues = self._analyze_semantic_opportunities(content, target_keywords or [])
        weaknesses.extend(semantic_issues)

        # Check for technical SEO elements
        technical_issues = self._analyze_technical_seo(content)
        weaknesses.extend(technical_issues)

        # Check for visual content opportunities (images)
        visual_issues = self._analyze_visual_content_opportunities(content, target_keywords or [])
        weaknesses.extend(visual_issues)

        return weaknesses

    def _analyze_keyword_usage(self, content: str, target_keywords: List[str]) -> List[Dict[str, Any]]:
        """Analyze keyword usage and density."""
        issues = []
        content_lower = content.lower()
        word_count = len(content.split())

        for keyword in target_keywords[:3]:  # Focus on top 3 keywords
            keyword_lower = keyword.lower()
            count = content_lower.count(keyword_lower)
            density = (count / word_count) * 100 if word_count > 0 else 0

            # Check keyword density (optimal: 1-3%)
            if density == 0:
                issues.append({
                    'type': 'keyword_missing',
                    'severity': 'high',
                    'issue': f"Keyword '{keyword}' not found in content",
                    'keyword': keyword,
                    'current_density': 0,
                    'target_density': '1-3%',
                    'impact': 'No ranking potential for this keyword'
                })
            elif density < 0.5:
                issues.append({
                    'type': 'keyword_low',
                    'severity': 'medium',
                    'issue': f"Low keyword density for '{keyword}'",
                    'keyword': keyword,
                    'current_density': round(density, 2),
                    'target_density': '1-3%',
                    'impact': 'Weak keyword signal'
                })
            elif density > 4:
                issues.append({
                    'type': 'keyword_stuffing',
                    'severity': 'high',
                    'issue': f"Keyword stuffing detected for '{keyword}'",
                    'keyword': keyword,
                    'current_density': round(density, 2),
                    'target_density': '1-3%',
                    'impact': 'Risk of Google penalty'
                })

        return issues

    def _analyze_semantic_opportunities(self, content: str, target_keywords: List[str]) -> List[Dict[str, Any]]:
        """Analyze semantic SEO opportunities."""
        issues = []
        content_lower = content.lower()

        # Check for semantic variations and related terms
        if target_keywords:
            main_keyword = target_keywords[0].lower()

            # Common semantic patterns
            semantic_patterns = {
                'seo': ['optimización', 'posicionamiento', 'ranking', 'google', 'búsqueda'],
                'marketing': ['publicidad', 'promoción', 'estrategia', 'campaña', 'digital'],
                'contenido': ['texto', 'artículo', 'información', 'redacción', 'copy']
            }

            if main_keyword in semantic_patterns:
                related_terms = semantic_patterns[main_keyword]
                found_terms = [term for term in related_terms if term in content_lower]

                if len(found_terms) < 2:
                    issues.append({
                        'type': 'semantic_gap',
                        'severity': 'medium',
                        'issue': f"Missing semantic variations for '{target_keywords[0]}'",
                        'missing_terms': [term for term in related_terms if term not in content_lower][:3],
                        'found_terms': found_terms,
                        'impact': 'Limited semantic relevance'
                    })

        return issues

    def _analyze_technical_seo(self, content: str) -> List[Dict[str, Any]]:
        """Analyze technical SEO elements."""
        issues = []

        # Check for call-to-action
        cta_patterns = ['contacta', 'descarga', 'regístrate', 'compra', 'solicita', 'obtén', 'aprende']
        has_cta = any(pattern in content.lower() for pattern in cta_patterns)

        if not has_cta and len(content.split()) > 100:
            issues.append({
                'type': 'missing_cta',
                'severity': 'medium',
                'issue': 'No clear call-to-action found',
                'suggestions': ['Contacta ahora', 'Descubre más', 'Solicita información'],
                'impact': 'Lower conversion rate'
            })

        # Check for emotional triggers
        emotional_words = ['increíble', 'sorprendente', 'revolucionario', 'exclusivo', 'garantizado', 'gratis']
        has_emotional = any(word in content.lower() for word in emotional_words)

        if not has_emotional and len(content.split()) > 50:
            issues.append({
                'type': 'low_emotional_appeal',
                'severity': 'low',
                'issue': 'Content lacks emotional triggers',
                'suggestions': emotional_words[:3],
                'impact': 'Lower engagement and CTR'
            })

        return issues

    def _analyze_visual_content_opportunities(self, content: str, target_keywords: List[str]) -> List[Dict[str, Any]]:
        """Analyze opportunities for visual content enhancement."""
        issues = []

        # Count existing images in content
        image_count = len(re.findall(r'<img[^>]*>|!\[.*?\]\(.*?\)', content))
        content_length = len(content.split())

        # Calculate recommended image count (1 image per 300-500 words)
        recommended_images = max(1, content_length // 400)

        if image_count < recommended_images:
            missing_images = recommended_images - image_count

            # Generate image suggestions based on keywords and content
            image_suggestions = self._generate_image_suggestions(content, target_keywords, missing_images)

            issues.append({
                'type': 'visual_content',
                'severity': 'medium' if missing_images <= 2 else 'high',
                'issue': f'Faltan {missing_images} imágenes para optimizar el contenido',
                'description': f'El contenido tiene {content_length} palabras pero solo {image_count} imágenes. Se recomiendan {recommended_images} imágenes.',
                'suggestion': 'Agregar imágenes relevantes mejora la experiencia del usuario y el SEO.',
                'image_suggestions': image_suggestions,
                'action': 'generate_images',
                'confidence': 0.85
            })

        # Check for alt text opportunities
        if image_count > 0:
            # This would need actual HTML parsing in a real implementation
            issues.append({
                'type': 'image_optimization',
                'severity': 'medium',
                'issue': 'Optimizar atributos ALT de imágenes existentes',
                'description': 'Asegurar que todas las imágenes tengan texto alternativo descriptivo.',
                'suggestion': 'Incluir palabras clave relevantes en los atributos ALT de las imágenes.',
                'action': 'optimize_alt_text',
                'confidence': 0.75
            })

        return issues

    def _generate_image_suggestions(self, content: str, target_keywords: List[str], count: int) -> List[Dict[str, Any]]:
        """Generate specific image suggestions based on content analysis."""
        suggestions = []

        # Extract main topics from content
        sentences = content.split('.')[:10]  # First 10 sentences for context
        content_topics = []

        for keyword in target_keywords[:3]:  # Top 3 keywords
            content_topics.append(keyword)

        # Add content-based topics
        common_business_terms = ['oficina', 'empresa', 'negocio', 'equipo', 'trabajo', 'profesional', 'servicio', 'cliente']
        for term in common_business_terms:
            if term.lower() in content.lower() and term not in content_topics:
                content_topics.append(term)
                if len(content_topics) >= 5:
                    break

        # Generate image prompts
        image_styles = ['realista', 'profesional', 'moderno', 'minimalista']

        for i in range(min(count, 4)):  # Max 4 suggestions
            if i < len(content_topics):
                topic = content_topics[i]
                style = image_styles[i % len(image_styles)]

                suggestions.append({
                    'prompt': f'Una imagen {style} de {topic}, ambiente profesional, alta calidad',
                    'alt_text': f'{topic.title()} - Imagen profesional para contenido SEO',
                    'keywords': [topic] + target_keywords[:2],
                    'style': style,
                    'aspect_ratio': 'ASPECT_16_9'
                })

        return suggestions

    def _calculate_weakness_confidence(self, weakness: Dict[str, Any], content_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate confidence score and visual indicators for a weakness."""

        # Base confidence calculation based on weakness type and severity
        base_confidence = self._get_base_confidence_score(weakness)

        # Adjust confidence based on content context
        context_adjustment = self._get_context_confidence_adjustment(weakness, content_analysis)

        # Calculate final confidence score
        confidence_score = max(0, min(100, base_confidence + context_adjustment))

        # Get visual indicators
        confidence_badge = self._get_confidence_badge(confidence_score)
        confidence_color = self._get_confidence_color(confidence_score)

        # Calculate improvement potential
        improvement_potential = self._calculate_improvement_potential(weakness, confidence_score)

        return {
            'confidence_score': round(confidence_score, 1),
            'confidence_badge': confidence_badge,
            'confidence_color': confidence_color,
            'improvement_potential': improvement_potential
        }

    def _get_base_confidence_score(self, weakness: Dict[str, Any]) -> float:
        """Get base confidence score based on weakness type and severity."""

        # Severity-based base scores
        severity_scores = {
            'high': 25.0,      # High severity = low confidence
            'medium': 50.0,    # Medium severity = medium confidence
            'low': 75.0        # Low severity = high confidence
        }

        base_score = severity_scores.get(weakness.get('severity', 'medium'), 50.0)

        # Type-specific adjustments
        type_adjustments = {
            'length': -10.0,           # Length issues are critical
            'seo_structure': -15.0,    # SEO structure is very important
            'keyword_density': -20.0,  # Keyword issues are critical
            'keyword_stuffing': -25.0, # Keyword stuffing is very bad
            'readability': 5.0,        # Readability is important but fixable
            'engagement': 10.0,        # Engagement is nice to have
            'semantic_gap': 0.0,       # Semantic gaps are moderate
            'missing_cta': 5.0,        # CTA is important but not critical
            'low_emotional_appeal': 15.0, # Emotional appeal is enhancement
            'visual_content': 10.0,    # Visual content is important for engagement
            'image_optimization': 5.0  # Image optimization is good practice
        }

        adjustment = type_adjustments.get(weakness.get('type', ''), 0.0)
        return base_score + adjustment

    def _get_context_confidence_adjustment(self, weakness: Dict[str, Any], content_analysis: Dict[str, Any]) -> float:
        """Adjust confidence based on content context."""

        adjustment = 0.0
        word_count = content_analysis.get('word_count', 0)

        # Content length context
        if word_count > 500:
            adjustment += 10.0  # Longer content = higher confidence in analysis
        elif word_count < 100:
            adjustment -= 15.0  # Very short content = lower confidence

        # Content structure context
        if content_analysis.get('has_headings', False):
            adjustment += 5.0   # Good structure = higher confidence

        if content_analysis.get('has_lists', False):
            adjustment += 3.0   # Lists indicate good structure

        # Readability context
        avg_sentence_length = content_analysis.get('avg_words_per_sentence', 15)
        if 12 <= avg_sentence_length <= 18:
            adjustment += 5.0   # Good readability = higher confidence

        return adjustment

    def _get_confidence_badge(self, score: float) -> str:
        """Get confidence badge text based on score."""
        if score >= 85:
            return "Excelente"
        elif score >= 70:
            return "Bueno"
        else:
            return "Mejorable"

    def _get_confidence_color(self, score: float) -> str:
        """Get confidence color code for UI styling."""
        if score >= 85:
            return "#22c55e"  # Green
        elif score >= 70:
            return "#f59e0b"  # Orange/Yellow
        else:
            return "#ef4444"  # Red

    def _calculate_improvement_potential(self, weakness: Dict[str, Any], confidence_score: float) -> Dict[str, Any]:
        """Calculate the potential impact of fixing this weakness."""

        # Base improvement potential based on weakness type
        type_impact = {
            'length': {'seo_impact': 85, 'user_impact': 60, 'effort': 'medium'},
            'seo_structure': {'seo_impact': 90, 'user_impact': 75, 'effort': 'low'},
            'keyword_density': {'seo_impact': 80, 'user_impact': 40, 'effort': 'low'},
            'keyword_stuffing': {'seo_impact': 95, 'user_impact': 70, 'effort': 'medium'},
            'readability': {'seo_impact': 70, 'user_impact': 85, 'effort': 'medium'},
            'engagement': {'seo_impact': 60, 'user_impact': 80, 'effort': 'low'},
            'semantic_gap': {'seo_impact': 75, 'user_impact': 50, 'effort': 'medium'},
            'missing_cta': {'seo_impact': 40, 'user_impact': 90, 'effort': 'low'},
            'low_emotional_appeal': {'seo_impact': 30, 'user_impact': 70, 'effort': 'low'}
        }

        weakness_type = weakness.get('type', 'engagement')
        impact_data = type_impact.get(weakness_type, {'seo_impact': 50, 'user_impact': 50, 'effort': 'medium'})

        # Adjust impact based on confidence score (lower confidence = higher potential impact)
        confidence_multiplier = (100 - confidence_score) / 100

        return {
            'seo_impact': round(impact_data['seo_impact'] * confidence_multiplier),
            'user_impact': round(impact_data['user_impact'] * confidence_multiplier),
            'effort_required': impact_data['effort'],
            'priority_score': round((impact_data['seo_impact'] + impact_data['user_impact']) * confidence_multiplier / 2)
        }

    def _calculate_overall_content_confidence(self, weaknesses: List[Dict[str, Any]], content_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall content confidence by aggregating individual weakness scores."""

        if not weaknesses:
            # No weaknesses found = excellent content
            overall_score = 95.0
        else:
            # Calculate weighted average of confidence scores
            total_weighted_score = 0.0
            total_weight = 0.0

            for weakness in weaknesses:
                confidence_score = weakness.get('confidence_score', 50.0)

                # Weight based on severity and impact
                severity_weight = {
                    'high': 3.0,
                    'medium': 2.0,
                    'low': 1.0
                }.get(weakness.get('severity', 'medium'), 2.0)

                # Additional weight based on improvement potential
                improvement_potential = weakness.get('improvement_potential', {})
                priority_score = improvement_potential.get('priority_score', 50)
                impact_weight = priority_score / 100.0

                final_weight = severity_weight * (1 + impact_weight)

                total_weighted_score += confidence_score * final_weight
                total_weight += final_weight

            overall_score = total_weighted_score / total_weight if total_weight > 0 else 50.0

        # Bonus for good content characteristics
        word_count = content_analysis.get('word_count', 0)
        if word_count >= 300:
            overall_score += 5.0
        if content_analysis.get('has_headings', False):
            overall_score += 3.0
        if content_analysis.get('has_lists', False):
            overall_score += 2.0

        # Ensure score is within bounds
        overall_score = max(0, min(100, overall_score))

        # Calculate quality metrics
        quality_metrics = self._calculate_quality_metrics(weaknesses, overall_score)

        return {
            'overall_score': round(overall_score, 1),
            'confidence_badge': self._get_confidence_badge(overall_score),
            'confidence_color': self._get_confidence_color(overall_score),
            'quality_level': self._get_quality_level(overall_score),
            'weaknesses_count': len(weaknesses),
            'high_priority_issues': len([w for w in weaknesses if w.get('severity') == 'high']),
            'improvement_opportunities': len([w for w in weaknesses if w.get('improvement_potential', {}).get('priority_score', 0) > 70]),
            'quality_metrics': quality_metrics
        }

    def _calculate_quality_metrics(self, weaknesses: List[Dict[str, Any]], overall_score: float) -> Dict[str, Any]:
        """Calculate detailed quality metrics for the content."""

        # Categorize weaknesses by type
        weakness_categories = {}
        for weakness in weaknesses:
            category = weakness.get('type', 'other')
            if category not in weakness_categories:
                weakness_categories[category] = []
            weakness_categories[category].append(weakness)

        # Calculate category scores
        category_scores = {}
        for category, category_weaknesses in weakness_categories.items():
            avg_confidence = sum(w.get('confidence_score', 50) for w in category_weaknesses) / len(category_weaknesses)
            category_scores[category] = {
                'score': round(avg_confidence, 1),
                'count': len(category_weaknesses),
                'badge': self._get_confidence_badge(avg_confidence)
            }

        return {
            'seo_structure_score': category_scores.get('seo_structure', {'score': 85, 'badge': 'Excelente'})['score'],
            'readability_score': category_scores.get('readability', {'score': 80, 'badge': 'Bueno'})['score'],
            'keyword_optimization_score': category_scores.get('keyword_density', {'score': 75, 'badge': 'Bueno'})['score'],
            'engagement_score': category_scores.get('engagement', {'score': 70, 'badge': 'Bueno'})['score'],
            'category_breakdown': category_scores
        }

    def _get_quality_level(self, score: float) -> str:
        """Get descriptive quality level based on score."""
        if score >= 90:
            return "Excelente - Contenido optimizado"
        elif score >= 80:
            return "Muy bueno - Pocas mejoras necesarias"
        elif score >= 70:
            return "Bueno - Algunas optimizaciones recomendadas"
        elif score >= 60:
            return "Regular - Varias mejoras necesarias"
        else:
            return "Mejorable - Optimización significativa requerida"

    def _estimate_readability(self, avg_words_per_sentence: float, long_sentences_count: int) -> str:
        """Estimate readability level."""
        if avg_words_per_sentence < 15 and long_sentences_count == 0:
            return 'excellent'
        elif avg_words_per_sentence < 20 and long_sentences_count < 2:
            return 'good'
        elif avg_words_per_sentence < 25:
            return 'fair'
        else:
            return 'poor'
    
    async def _generate_contextual_suggestions(
        self, 
        content: str, 
        analysis: Dict[str, Any], 
        weaknesses: List[Dict[str, Any]], 
        target_keywords: List[str] = None
    ) -> List[str]:
        """Generate specific, contextual suggestions based on the actual content."""
        suggestions = []
        
        # Generate suggestions based on identified weaknesses
        for weakness in weaknesses:
            suggestion = self._create_specific_suggestion(content, weakness, analysis)
            if suggestion:
                suggestions.append(suggestion)
        
        # Add keyword-specific suggestions
        if target_keywords:
            keyword_suggestions = self._generate_keyword_suggestions(content, target_keywords)
            suggestions.extend(keyword_suggestions)
        
        # Add content-specific improvements
        content_suggestions = self._generate_content_specific_suggestions(content, analysis)
        suggestions.extend(content_suggestions)
        
        # Remove duplicates and limit to top 5
        unique_suggestions = list(dict.fromkeys(suggestions))
        return unique_suggestions[:5]
    
    def _create_specific_suggestion(self, content: str, weakness: Dict[str, Any], analysis: Dict[str, Any]) -> str:
        """Create a specific, educational suggestion based on a weakness."""
        weakness_type = weakness['type']
        topic = self._extract_main_topic(content)

        if weakness_type == 'length':
            current_words = analysis['word_count']
            return f"📝 **Expande tu contenido de {current_words} a 300+ palabras sobre '{topic}'**\n\n**¿Por qué?** Google favorece contenido completo que responda todas las preguntas del usuario. Contenido corto (<150 palabras) raramente rankea en primera página.\n\n**Cómo hacerlo:**\n• Agrega una definición clara de '{topic}'\n• Incluye 2-3 beneficios específicos\n• Menciona casos de uso reales\n• Añade estadísticas o datos relevantes\n\n**Impacto SEO:** +25-40% probabilidad de ranking en top 10"

        elif weakness_type == 'structure':
            return f"🏗️ **Estructura tu contenido en {weakness['target_value']} con párrafos temáticos**\n\n**¿Por qué?** Los algoritmos de Google analizan la estructura para entender el contenido. Párrafos bien organizados mejoran la experiencia del usuario y el tiempo de permanencia.\n\n**Cómo hacerlo:**\n• Párrafo 1: Introducción a '{topic}'\n• Párrafo 2: Problema que resuelve\n• Párrafo 3: Solución/beneficios\n• Párrafo 4: Conclusión/llamada a la acción\n\n**Impacto SEO:** Mejora el 'dwell time' y reduce bounce rate en 15-30%"

        elif weakness_type == 'readability':
            avg_words = weakness['current_value'].split()[0]
            return f"✂️ **Simplifica oraciones largas (actualmente {avg_words} palabras/oración)**\n\n**¿Por qué?** Google usa métricas de legibilidad para evaluar la calidad del contenido. Oraciones largas (>20 palabras) dificultan la comprensión y aumentan el bounce rate.\n\n**Cómo hacerlo:**\n• Divide oraciones largas en 2-3 más cortas\n• Usa conectores: 'Además', 'Por tanto', 'Sin embargo'\n• Elimina palabras innecesarias\n• Objetivo: 15-18 palabras por oración\n\n**Impacto SEO:** Mejora el Core Web Vitals y la experiencia del usuario"

        elif weakness_type == 'engagement':
            return f"❓ **Agrega preguntas estratégicas sobre '{topic}' para aumentar engagement**\n\n**¿Por qué?** Las preguntas aumentan el tiempo de lectura y pueden aparecer en 'People Also Ask' de Google, generando tráfico adicional.\n\n**Cómo hacerlo:**\n• Pregunta inicial: '¿Sabías que {topic} puede...?'\n• Pregunta media: '¿Te has preguntado por qué...?'\n• Pregunta final: '¿Estás listo para implementar...?'\n• Usa preguntas que tu audiencia realmente se hace\n\n**Impacto SEO:** +20-35% tiempo en página, posible aparición en PAA"

        elif weakness_type == 'seo_structure':
            if 'headings' in weakness['issue']:
                return f"📋 **Estructura con encabezados H2/H3 para mejorar SEO técnico**\n\n**¿Por qué?** Los encabezados son señales directas para Google sobre la jerarquía y temas del contenido. El 70% del contenido top-ranking usa estructura H1>H2>H3.\n\n**Cómo hacerlo:**\n• H2: 'Qué es {topic}' (incluye keyword principal)\n• H2: 'Beneficios de {topic}' (incluye variaciones)\n• H2: 'Cómo implementar {topic}' (long-tail keywords)\n• H3: Subtemas específicos bajo cada H2\n\n**Impacto SEO:** +30-50% mejor comprensión por crawlers, featured snippets"
            elif 'lists' in weakness['issue']:
                return f"📝 **Organiza información en listas para mejorar estructura SEO**\n\n**¿Por qué?** Las listas mejoran la legibilidad y son favorecidas por Google para featured snippets. El 43% de los featured snippets son listas.\n\n**Cómo hacerlo:**\n• Lista numerada: Pasos o procesos sobre '{topic}'\n• Lista con viñetas: Beneficios o características\n• Usa 3-7 elementos por lista (óptimo para snippets)\n• Incluye keywords en los elementos de la lista\n\n**Impacto SEO:** +60% probabilidad de featured snippet, mejor CTR"

        elif weakness_type == 'keyword_missing':
            keyword = weakness['keyword']
            return f"🎯 **Incluye la keyword '{keyword}' estratégicamente en tu contenido**\n\n**¿Por qué?** Sin la keyword principal, Google no puede entender de qué trata tu contenido. Es imposible rankear para términos que no aparecen en el texto.\n\n**Cómo hacerlo:**\n• Primera mención: En los primeros 100 caracteres\n• Título/encabezado: Incluye '{keyword}' naturalmente\n• Párrafo principal: Define qué es '{keyword}'\n• Conclusión: Menciona '{keyword}' en el resumen\n\n**Impacto SEO:** +200-500% probabilidad de ranking para '{keyword}'"

        elif weakness_type == 'keyword_low':
            keyword = weakness['keyword']
            current = weakness['current_density']
            return f"📈 **Aumenta la densidad de '{keyword}' de {current}% a 1-3%**\n\n**¿Por qué?** Densidad muy baja indica poca relevancia para Google. La densidad óptima (1-3%) señala que el contenido es realmente sobre ese tema.\n\n**Cómo hacerlo:**\n• Agrega '{keyword}' en 1-2 lugares más\n• Usa variaciones: '{keyword}s', 'de {keyword}', 'para {keyword}'\n• Incluye en meta descripción y alt text\n• Mantén naturalidad en el texto\n\n**Impacto SEO:** +30-60% relevancia temática, mejor ranking"

        elif weakness_type == 'keyword_stuffing':
            keyword = weakness['keyword']
            current = weakness['current_density']
            return f"⚠️ **Reduce keyword stuffing de '{keyword}' ({current}% → 1-3%)**\n\n**¿Por qué?** Densidad >4% es considerada spam por Google y puede resultar en penalizaciones. Afecta negativamente el ranking.\n\n**Cómo hacerlo:**\n• Reemplaza algunas menciones con sinónimos\n• Usa pronombres: 'esto', 'ello', 'esta técnica'\n• Incluye variaciones semánticas\n• Enfócate en naturalidad y fluidez\n\n**Impacto SEO:** Evita penalizaciones, mejora experiencia del usuario"

        elif weakness_type == 'semantic_gap':
            missing_terms = weakness.get('missing_terms', [])
            main_keyword = weakness['issue'].split("'")[1]
            return f"🔗 **Agrega términos relacionados para mejorar relevancia semántica**\n\n**¿Por qué?** Google usa LSI (Latent Semantic Indexing) para entender el contexto. Contenido con términos relacionados rankea mejor.\n\n**Cómo hacerlo:**\n• Incluye naturalmente: {', '.join(missing_terms[:3])}\n• Usa en contexto relevante sobre '{main_keyword}'\n• Crea párrafos que conecten estos conceptos\n• Mantén coherencia temática\n\n**Impacto SEO:** +25-40% relevancia semántica, mejor comprensión por IA"

        elif weakness_type == 'missing_cta':
            suggestions = weakness.get('suggestions', ['Contacta ahora', 'Descubre más'])
            return f"📞 **Agrega una llamada a la acción clara para mejorar conversiones**\n\n**¿Por qué?** Google mide engagement y tiempo en página. CTAs claros aumentan interacción y señalan contenido valioso.\n\n**Cómo hacerlo:**\n• Final del contenido: '{suggestions[0]}' o '{suggestions[1]}'\n• Usa verbos de acción: 'Descubre', 'Obtén', 'Solicita'\n• Crea urgencia: 'Hoy mismo', 'Ahora'\n• Haz específico el beneficio\n\n**Impacto SEO:** +15-25% tiempo en página, mejor engagement"

        elif weakness_type == 'low_emotional_appeal':
            suggestions = weakness.get('suggestions', ['increíble', 'sorprendente', 'exclusivo'])
            return f"💫 **Agrega palabras emocionales para aumentar engagement**\n\n**¿Por qué?** Contenido emocional genera más clics, shares y tiempo de lectura. Google premia contenido que genera engagement.\n\n**Cómo hacerlo:**\n• Usa palabras como: '{suggestions[0]}', '{suggestions[1]}', '{suggestions[2]}'\n• Aplica en títulos y primeras líneas\n• Combina con datos: 'Resultados increíbles: +300%'\n• Mantén autenticidad y relevancia\n\n**Impacto SEO:** +20-35% CTR, mejor engagement metrics"

        return None
    
    def _extract_main_topic(self, content: str) -> str:
        """Extract the main topic from content."""
        words = content.split()
        if len(words) > 0:
            # Simple topic extraction - get most frequent meaningful word
            word_freq = {}
            for word in words:
                clean_word = re.sub(r'[^\w]', '', word.lower())
                if len(clean_word) > 4 and clean_word not in ['para', 'este', 'esta', 'como', 'pero', 'todo']:
                    word_freq[clean_word] = word_freq.get(clean_word, 0) + 1
            
            if word_freq:
                return max(word_freq.items(), key=lambda x: x[1])[0]
        
        return "el tema"
    
    def _generate_keyword_suggestions(self, content: str, target_keywords: List[str]) -> List[str]:
        """Generate keyword-specific suggestions."""
        suggestions = []
        content_lower = content.lower()
        
        for keyword in target_keywords:
            if keyword.lower() not in content_lower:
                suggestions.append(f"Incluye la palabra clave '{keyword}' de forma natural en tu contenido, especialmente en el primer párrafo")
            elif content_lower.count(keyword.lower()) < 2:
                suggestions.append(f"Menciona '{keyword}' al menos 2-3 veces a lo largo del contenido para mejorar la relevancia SEO")
        
        return suggestions
    
    def _generate_content_specific_suggestions(self, content: str, analysis: Dict[str, Any]) -> List[str]:
        """Generate suggestions specific to the content characteristics."""
        suggestions = []
        
        # Specific improvements based on content analysis
        if analysis['technical_terms_count'] == 0 and analysis['word_count'] > 100:
            topic = self._extract_main_topic(content)
            suggestions.append(f"Incluye terminología más específica y técnica relacionada con {topic} para aumentar la autoridad del contenido")
        
        if analysis['word_frequency']:
            most_common_word = list(analysis['word_frequency'].keys())[0]
            suggestions.append(f"Considera expandir el concepto de '{most_common_word}' con definiciones y ejemplos específicos")
        
        return suggestions
    
    def _calculate_contextual_scores(self, analysis: Dict[str, Any], weaknesses: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate contextual scores based on analysis."""
        base_score = 100
        
        # Deduct points for each weakness
        for weakness in weaknesses:
            if weakness['severity'] == 'high':
                base_score -= 25
            elif weakness['severity'] == 'medium':
                base_score -= 15
            elif weakness['severity'] == 'low':
                base_score -= 10
        
        # Ensure minimum score
        final_score = max(base_score, 20)
        
        return {
            'overall_score': final_score,
            'readability_score': self._get_readability_score(analysis['readability_estimate']),
            'structure_score': min(100, (analysis['paragraph_count'] * 20) + (analysis['heading_count'] * 15)),
            'engagement_score': min(100, (analysis['questions_count'] * 30) + (analysis['has_lists'] * 20))
        }
    
    def _get_readability_score(self, readability: str) -> float:
        """Convert readability estimate to score."""
        scores = {'excellent': 95, 'good': 80, 'fair': 65, 'poor': 40}
        return scores.get(readability, 50)
    
    def _get_fallback_analysis(self, content: str) -> Dict[str, Any]:
        """Provide fallback analysis if main analysis fails."""
        words = content.split()
        sentences = [s.strip() for s in re.split(r'[.!?]+', content) if s.strip()]
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]

        return {
            'content_analysis': {
                'word_count': len(words),
                'sentence_count': len(sentences),
                'paragraph_count': len(paragraphs)
            },
            'weaknesses': [],
            'suggestions': ['Mejora la estructura del contenido', 'Agrega más detalles específicos'],
            'scores': {
                'overall_score': 50,
                'readability_score': 50,
                'structure_score': 50,
                'engagement_score': 50
            },
            'analysis_type': 'fallback'
        }
