"""
Runway ML Service for image and video generation.

This service provides functionality to:
1. Generate images from text prompts using Runway's gen4_image model
2. Convert images to videos using Runway's gen4_turbo model
"""

import logging
import base64
import os
from typing import Dict, Any, Optional, List
from runwayml import RunwayML, TaskFailedError
from app.core.config import settings

logger = logging.getLogger(__name__)

class RunwayService:
    """Service for interacting with Runway ML API."""
    
    def __init__(self):
        """Initialize the Runway service with API key."""
        if not settings.RUNWAY_API_KEY:
            logger.error("RUNWAY_API_KEY not configured")
            raise ValueError("Runway API key not configured")
        
        # Set the API key as environment variable for the SDK
        os.environ["RUNWAYML_API_SECRET"] = settings.RUNWAY_API_KEY
        self.client = RunwayML()
        logger.info("Runway ML service initialized successfully")
    
    async def generate_image(
        self,
        prompt_text: str,
        ratio: str = "1280:720",
        seed: Optional[int] = None,
        reference_images: Optional[List[Dict[str, str]]] = None
    ) -> Dict[str, Any]:
        """
        Generate an image from text using Runway's gen4_image model.
        
        Args:
            prompt_text: Text description of the image to generate
            ratio: Image aspect ratio (default: "1280:720")
            seed: Random seed for reproducible results
            reference_images: List of reference images with uri and tag
            
        Returns:
            Dict containing task result with image URL
        """
        try:
            logger.info(f"Generating image with prompt: '{prompt_text[:100]}...'")
            
            # Validate ratio
            valid_ratios = [
                "1920:1080", "1080:1920", "1024:1024", "1360:768", "1080:1080",
                "1168:880", "1440:1080", "1080:1440", "1808:768", "2112:912",
                "1280:720", "720:1280", "720:720", "960:720", "720:960", "1680:720"
            ]
            if ratio not in valid_ratios:
                ratio = "1280:720"  # Default fallback
            
            # Prepare request parameters
            request_params = {
                "model": "gen4_image",
                "prompt_text": prompt_text,
                "ratio": ratio
            }
            
            if seed is not None:
                request_params["seed"] = seed
                
            if reference_images:
                request_params["reference_images"] = reference_images
            
            # Generate image
            task = self.client.text_to_image.create(**request_params).wait_for_task_output()
            
            logger.info(f"Image generation completed successfully. Task ID: {task.id}")
            
            return {
                "success": True,
                "task_id": task.id,
                "status": task.status,
                "output": task.output,
                "image_url": task.output[0] if task.output else None
            }
            
        except TaskFailedError as e:
            logger.error(f"Runway image generation failed: {e.task_details}")
            return {
                "success": False,
                "error": "Image generation failed",
                "details": str(e.task_details)
            }
        except Exception as e:
            logger.error(f"Unexpected error in image generation: {str(e)}")
            return {
                "success": False,
                "error": "Unexpected error occurred",
                "details": str(e)
            }
    
    async def image_to_video(
        self,
        prompt_image: str,
        prompt_text: str = "Generate a video",
        ratio: str = "1280:720",
        duration: int = 5
    ) -> Dict[str, Any]:
        """
        Convert an image to video using Runway's gen4_turbo model.
        
        Args:
            prompt_image: URL or data URI of the source image
            prompt_text: Text description for video generation
            ratio: Video aspect ratio (default: "1280:720")
            duration: Video duration in seconds (5 or 10)
            
        Returns:
            Dict containing task result with video URL
        """
        try:
            logger.info(f"Converting image to video with prompt: '{prompt_text[:100]}...'")
            
            # Validate ratio for video
            valid_video_ratios = [
                "1280:720", "720:1280", "1104:832", "832:1104", "960:960", "1584:672"
            ]
            if ratio not in valid_video_ratios:
                ratio = "1280:720"  # Default fallback
            
            # Validate duration
            if duration not in [5, 10]:
                duration = 5  # Default fallback
            
            # Generate video
            task = self.client.image_to_video.create(
                model="gen4_turbo",
                prompt_image=prompt_image,
                prompt_text=prompt_text,
                ratio=ratio,
                duration=duration
            ).wait_for_task_output()
            
            logger.info(f"Video generation completed successfully. Task ID: {task.id}")
            
            return {
                "success": True,
                "task_id": task.id,
                "status": task.status,
                "output": task.output,
                "video_url": task.output[0] if task.output else None
            }
            
        except TaskFailedError as e:
            logger.error(f"Runway video generation failed: {e.task_details}")
            return {
                "success": False,
                "error": "Video generation failed",
                "details": str(e.task_details)
            }
        except Exception as e:
            logger.error(f"Unexpected error in video generation: {str(e)}")
            return {
                "success": False,
                "error": "Unexpected error occurred",
                "details": str(e)
            }
    
    def encode_image_to_data_uri(self, image_path: str) -> str:
        """
        Encode a local image file to a data URI.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Data URI string
        """
        try:
            with open(image_path, "rb") as f:
                base64_image = base64.b64encode(f.read()).decode("utf-8")
                # Determine content type based on file extension
                if image_path.lower().endswith('.png'):
                    content_type = "image/png"
                elif image_path.lower().endswith('.jpg') or image_path.lower().endswith('.jpeg'):
                    content_type = "image/jpeg"
                elif image_path.lower().endswith('.webp'):
                    content_type = "image/webp"
                else:
                    content_type = "image/png"  # Default fallback
                
                return f"data:{content_type};base64,{base64_image}"
        except Exception as e:
            logger.error(f"Error encoding image to data URI: {str(e)}")
            raise

# Global service instance
runway_service = RunwayService()
