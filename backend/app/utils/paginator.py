"""
Paginator Utility
Pagination helpers for SEO GPT Optimizer
"""

from typing import TypeV<PERSON>, Generic, List, Optional
from pydantic import BaseModel
from sqlalchemy.orm import Query

T = TypeVar('T')


class PaginationParams(BaseModel):
    """Standard pagination parameters"""
    limit: int = 20
    offset: int = 0
    
    def __post_init__(self):
        # Ensure reasonable limits
        if self.limit > 100:
            self.limit = 100
        if self.limit < 1:
            self.limit = 1
        if self.offset < 0:
            self.offset = 0


class PaginatedResponse(BaseModel, Generic[T]):
    """Generic paginated response model"""
    items: List[T]
    total: int
    limit: int
    offset: int
    has_next: bool
    has_previous: bool
    
    @classmethod
    def create(
        cls,
        items: List[T],
        total: int,
        limit: int,
        offset: int
    ) -> 'PaginatedResponse[T]':
        """Create a paginated response"""
        return cls(
            items=items,
            total=total,
            limit=limit,
            offset=offset,
            has_next=offset + limit < total,
            has_previous=offset > 0
        )


def paginate_query(
    query: Query,
    limit: int = 20,
    offset: int = 0
) -> tuple[List, int]:
    """
    Paginate a SQLAlchemy query
    
    Args:
        query: SQLAlchemy query object
        limit: Number of items per page
        offset: Number of items to skip
    
    Returns:
        Tuple of (items, total_count)
    """
    # Ensure reasonable limits
    limit = min(max(limit, 1), 100)
    offset = max(offset, 0)
    
    # Get total count
    total = query.count()
    
    # Get paginated items
    items = query.offset(offset).limit(limit).all()
    
    return items, total


def calculate_pagination_info(
    total: int,
    limit: int,
    offset: int
) -> dict:
    """
    Calculate pagination metadata
    
    Args:
        total: Total number of items
        limit: Items per page
        offset: Current offset
    
    Returns:
        Dictionary with pagination info
    """
    current_page = (offset // limit) + 1
    total_pages = (total + limit - 1) // limit  # Ceiling division
    
    return {
        "current_page": current_page,
        "total_pages": total_pages,
        "total_items": total,
        "items_per_page": limit,
        "has_next": offset + limit < total,
        "has_previous": offset > 0,
        "next_offset": offset + limit if offset + limit < total else None,
        "previous_offset": max(0, offset - limit) if offset > 0 else None
    }


class Paginator:
    """Utility class for handling pagination"""
    
    def __init__(self, limit: int = 20, max_limit: int = 100):
        self.default_limit = limit
        self.max_limit = max_limit
    
    def validate_params(self, limit: Optional[int], offset: Optional[int]) -> PaginationParams:
        """Validate and normalize pagination parameters"""
        if limit is None:
            limit = self.default_limit
        if offset is None:
            offset = 0
        
        # Apply constraints
        limit = min(max(limit, 1), self.max_limit)
        offset = max(offset, 0)
        
        return PaginationParams(limit=limit, offset=offset)
    
    def paginate_query(self, query: Query, limit: int, offset: int) -> PaginatedResponse:
        """Paginate a query and return a structured response"""
        items, total = paginate_query(query, limit, offset)
        return PaginatedResponse.create(items, total, limit, offset)


# Default paginator instance
default_paginator = Paginator()
