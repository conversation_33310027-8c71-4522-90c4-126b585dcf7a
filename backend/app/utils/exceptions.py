"""
Exceptions Utility
Custom exceptions and error handlers for SEO GPT Optimizer
"""

from typing import Any, Dict, Optional
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from fastapi.responses import JSONResponse


class SEOGPTException(Exception):
    """Base exception for SEO GPT Optimizer"""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ProjectNotFoundException(SEOGPTException):
    """Raised when a project is not found"""
    
    def __init__(self, project_id: str):
        super().__init__(
            message=f"Project not found: {project_id}",
            error_code="PROJECT_NOT_FOUND",
            details={"project_id": project_id}
        )


class ContentNotFoundException(SEOGPTException):
    """Raised when content is not found"""
    
    def __init__(self, content_id: str, project_id: Optional[str] = None):
        details = {"content_id": content_id}
        if project_id:
            details["project_id"] = project_id
        
        super().__init__(
            message=f"Content not found: {content_id}",
            error_code="CONTENT_NOT_FOUND",
            details=details
        )


class ResearchFailedException(SEOGPTException):
    """Raised when research fails"""
    
    def __init__(self, topic: str, reason: str):
        super().__init__(
            message=f"Research failed for topic '{topic}': {reason}",
            error_code="RESEARCH_FAILED",
            details={"topic": topic, "reason": reason}
        )


class AnalysisFailedException(SEOGPTException):
    """Raised when content analysis fails"""
    
    def __init__(self, analysis_type: str, reason: str):
        super().__init__(
            message=f"{analysis_type} analysis failed: {reason}",
            error_code="ANALYSIS_FAILED",
            details={"analysis_type": analysis_type, "reason": reason}
        )


class GenerationFailedException(SEOGPTException):
    """Raised when content generation fails"""
    
    def __init__(self, generation_type: str, reason: str):
        super().__init__(
            message=f"{generation_type} generation failed: {reason}",
            error_code="GENERATION_FAILED",
            details={"generation_type": generation_type, "reason": reason}
        )


class ImageGenerationFailedException(SEOGPTException):
    """Raised when image generation fails"""
    
    def __init__(self, reason: str):
        super().__init__(
            message=f"Image generation failed: {reason}",
            error_code="IMAGE_GENERATION_FAILED",
            details={"reason": reason}
        )


class ValidationException(SEOGPTException):
    """Raised when validation fails"""
    
    def __init__(self, field: str, value: Any, reason: str):
        super().__init__(
            message=f"Validation failed for {field}: {reason}",
            error_code="VALIDATION_FAILED",
            details={"field": field, "value": str(value), "reason": reason}
        )


def create_error_response(
    status_code: int,
    message: str,
    error_code: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> JSONResponse:
    """Create a standardized error response"""
    
    content = {
        "status": "error",
        "message": message,
        "error_code": error_code,
        "details": details or {}
    }
    
    return JSONResponse(
        status_code=status_code,
        content=content
    )


def seo_gpt_exception_to_http(exception: SEOGPTException) -> HTTPException:
    """Convert SEO GPT exception to HTTP exception"""
    
    # Map exception types to HTTP status codes
    status_map = {
        ProjectNotFoundException: status.HTTP_404_NOT_FOUND,
        ContentNotFoundException: status.HTTP_404_NOT_FOUND,
        ResearchFailedException: status.HTTP_500_INTERNAL_SERVER_ERROR,
        AnalysisFailedException: status.HTTP_500_INTERNAL_SERVER_ERROR,
        GenerationFailedException: status.HTTP_500_INTERNAL_SERVER_ERROR,
        ImageGenerationFailedException: status.HTTP_500_INTERNAL_SERVER_ERROR,
        ValidationException: status.HTTP_400_BAD_REQUEST,
    }
    
    status_code = status_map.get(type(exception), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return HTTPException(
        status_code=status_code,
        detail={
            "message": exception.message,
            "error_code": exception.error_code,
            "details": exception.details
        }
    )


# Error handler decorators
def handle_seo_gpt_exceptions(func):
    """Decorator to handle SEO GPT exceptions"""
    
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except SEOGPTException as e:
            raise seo_gpt_exception_to_http(e)
        except Exception as e:
            # Log unexpected exceptions
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Unexpected error in {func.__name__}: {str(e)}")
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "An unexpected error occurred",
                    "error_code": "INTERNAL_ERROR",
                    "details": {"function": func.__name__}
                }
            )
    
    return wrapper
