"""
Logger Utility
Centralized logging configuration for SEO GPT Optimizer
"""

import logging
import sys
from typing import Optional


def setup_logger(
    name: str,
    level: str = "INFO",
    format_string: Optional[str] = None
) -> logging.Logger:
    """
    Set up a logger with consistent formatting
    
    Args:
        name: Logger name
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_string: Custom format string
    
    Returns:
        Configured logger instance
    """
    
    # Default format string
    if format_string is None:
        format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Create logger
    logger = logging.getLogger(name)
    
    # Set level
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(numeric_level)
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)
    
    # Create formatter
    formatter = logging.Formatter(format_string)
    console_handler.setFormatter(formatter)
    
    # Add handler to logger
    logger.addHandler(console_handler)
    
    return logger


def get_seo_gpt_logger(module_name: str) -> logging.Logger:
    """
    Get a logger specifically configured for SEO GPT Optimizer modules
    
    Args:
        module_name: Name of the module requesting the logger
    
    Returns:
        Configured logger instance
    """
    logger_name = f"seo_gpt.{module_name}"
    
    # Custom format for SEO GPT Optimizer
    format_string = "%(asctime)s - 🧠 SEO GPT - %(name)s - %(levelname)s - %(message)s"
    
    return setup_logger(logger_name, "INFO", format_string)


# Pre-configured loggers for common modules
project_logger = get_seo_gpt_logger("projects")
content_logger = get_seo_gpt_logger("contents")
research_logger = get_seo_gpt_logger("research")
analysis_logger = get_seo_gpt_logger("analysis")
generation_logger = get_seo_gpt_logger("generation")
image_logger = get_seo_gpt_logger("images")
