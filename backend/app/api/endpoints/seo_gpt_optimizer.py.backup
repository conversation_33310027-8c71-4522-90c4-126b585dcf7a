"""
SEO & GPT Optimizer™ - API Endpoints
Main API endpoints for the SEO & GPT Optimizer tool

This module provides comprehensive SEO and content optimization endpoints including:
- Project management (CRUD operations)
- Content analysis and optimization
- SEO research and keyword analysis
- AI-powered content generation
- SAIO (Search AI Optimization) features
- Image generation with Ideogram AI integration
- Analytics and reporting
"""

import logging
import uuid
import os
import aiohttp
import aiofiles
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse, Response
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.db.session import get_db
from app.db.seo_gpt_models import SEOGPTProject, ContentAnalysis, GPTRankHistory, KeywordResearch, ProjectContent, ProjectStatus, ContentType
from app.services.seo_gpt_research import SEOGPTResearchService
from app.services.gpt_rank_engine import GPTRankEngineService
from app.services.ai_content_generator import RealAIContentGenerator, ContentGenerationRequest as AIContentRequest
from app.services.ideogram_service import IdeogramService
from app.services.saio_agent_service import SAIOAgentService
from app.services.contextual_seo_analyzer import ContextualSEOAnalyzer
from app.core.config import settings

# Configure logging
logger = logging.getLogger(__name__)

# Initialize FastAPI router
router = APIRouter()

# ============================================================================
# SERVICE INITIALIZATION
# ============================================================================

# Initialize core services
research_service = SEOGPTResearchService()
gpt_rank_service = GPTRankEngineService()
ai_content_generator = RealAIContentGenerator(
    openai_api_key=settings.OPENAI_API_KEY,
    gemini_api_key=settings.GEMINI_API_KEY
)
ideogram_service = IdeogramService()
saio_agent_service = SAIOAgentService()
contextual_analyzer = ContextualSEOAnalyzer()

# ============================================================================
# PYDANTIC MODELS - REQUEST/RESPONSE SCHEMAS
# ============================================================================

# Research and Analysis Models
class ResearchRequest(BaseModel):
    """Request model for SEO research and keyword analysis"""
    topic: str = Field(..., description="Main topic/keyword to research")
    target_language: str = Field(default="es", description="Target language for analysis")
    include_reddit: bool = Field(default=True, description="Include Reddit insights")
    include_quora: bool = Field(default=True, description="Include Quora insights")
    # Project linking
    project_id: Optional[str] = Field(None, description="Project ID to link research to")
    # Geographic targeting
    target_country: Optional[str] = Field(default="ES", description="Target country code")
    # Additional sources
    include_news: bool = Field(default=False, description="Include news articles")
    # Competitive analysis
    competitor_domains: Optional[List[str]] = Field(default=[], description="Competitor domains to analyze")

# Project Management Models
class ProjectCreateRequest(BaseModel):
    """Request model for creating new SEO projects"""
    title: str = Field(..., description="Project title")
    description: Optional[str] = Field(None, description="Project description")
    topic: Optional[str] = Field(None, description="Main topic/keyword")
    target_language: str = Field(default="es", description="Target language")
    content_type: ContentType = Field(default=ContentType.ARTICLE, description="Type of content")
    target_gpt_rank_score: float = Field(default=80.0, description="Target GPT Rank score")
    user_id: Optional[str] = Field(None, description="User ID")

# Content Analysis Models
class ContentAnalysisRequest(BaseModel):
    """Request model for content SEO analysis"""
    project_id: str = Field(..., description="Project ID")
    content: str = Field(..., description="Content to analyze")
    target_keywords: Optional[List[str]] = Field(default=None, description="Target keywords for analysis")
    content_type: Optional[str] = Field(default="blog", description="Type of content")
    deep_analysis: bool = Field(default=False, description="Whether to perform deep contextual analysis")

class ContentUpdateRequest(BaseModel):
    """Request model for updating project content"""
    project_id: str = Field(..., description="Project ID")
    content: str = Field(..., description="Updated content")
    title: Optional[str] = Field(None, description="Updated title")

# Project Content Management Models
class ProjectContentCreateRequest(BaseModel):
    """Request model for creating project content"""
    title: str = Field(..., description="Content title")
    content_type: str = Field(default="blog", description="Type of content: blog, article, research, guide")
    content_text: Optional[str] = Field(None, description="Content text")
    content_html: Optional[str] = Field(None, description="Content HTML")
    target_keywords: Optional[List[str]] = Field(default=[], description="Target keywords")
    meta_description: Optional[str] = Field(None, description="Meta description")
    slug: Optional[str] = Field(None, description="URL slug")

class ProjectContentUpdateRequest(BaseModel):
    """Request model for updating project content"""
    title: Optional[str] = Field(None, description="Content title")
    content_text: Optional[str] = Field(None, description="Content text")
    content_html: Optional[str] = Field(None, description="Content HTML")
    status: Optional[str] = Field(None, description="Content status: draft, published, archived")
    target_keywords: Optional[List[str]] = Field(None, description="Target keywords")
    meta_description: Optional[str] = Field(None, description="Meta description")
    slug: Optional[str] = Field(None, description="URL slug")
    current_gpt_rank_score: Optional[float] = Field(None, description="Current GPT Rank score")
    seo_score: Optional[float] = Field(None, description="SEO score")

# Content Generation Models
class BlogGenerationRequest(BaseModel):
    """Request model for AI blog content generation"""
    project_id: str = Field(..., description="Project ID")
    topic: str = Field(..., description="Blog topic")
    content_type: str = Field(default="educational", description="Type of content (educational, motivational, balanced)")
    include_images: bool = Field(default=True, description="Whether to generate images")
    num_images: int = Field(default=3, description="Number of images to generate")
    target_length: str = Field(default="medium", description="Target length (short, medium, long)")

# SAIO (Search AI Optimization) Models
class SAIOContentRequest(BaseModel):
    """Request model for SAIO-optimized content generation"""
    project_id: str = Field(..., description="Project ID")
    topic: str = Field(..., description="Content topic")
    content_type: str = Field(default="how_to", description="SAIO template type (how_to, list_article, comparison)")
    optimize_for: List[str] = Field(default=["chatgpt", "perplexity", "google_sge"], description="AI platforms to optimize for")

# SEO Image Generation Models
class SEOImageGenerationRequest(BaseModel):
    """Request model for SEO-optimized image generation with Ideogram AI"""
    project_id: str = Field(..., description="Project ID")
    prompt: str = Field(..., description="Image description/prompt")
    style: str = Field(default="realista", description="Image style (realista, profesional, moderno, minimalista)")
    aspect_ratio: str = Field(default="1:1", description="Image aspect ratio (1:1, 16:9, 9:16)")
    keywords: List[str] = Field(default=[], description="SEO keywords to incorporate")
    alt_text: Optional[str] = Field(None, description="Suggested alt text for SEO")

# ============================================================================
# PROJECT MANAGEMENT ENDPOINTS
# ============================================================================

@router.post("/projects/create")
async def create_project(
    request: ProjectCreateRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Create a new SEO GPT Optimizer project.

    This endpoint creates a new project for organizing content generation
    and analysis work within the SEO GPT Optimizer.
    """
    try:
        project_id = str(uuid.uuid4())

        # Create new project
        new_project = SEOGPTProject(
            project_id=project_id,
            title=request.title,
            topic=request.topic or request.title,
            target_language=request.target_language,
            content_type=request.content_type,
            target_gpt_rank_score=request.target_gpt_rank_score,
            status=ProjectStatus.DRAFT,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        db.add(new_project)
        db.commit()

        logger.info(f"✅ Created new SEO GPT project: {project_id}")

        return JSONResponse(
            status_code=201,
            content={
                "status": "success",
                "project_id": project_id,
                "message": "Project created successfully"
            }
        )

    except Exception as e:
        logger.error(f"❌ Project creation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Project creation failed: {str(e)}")

@router.post("/projects")
async def create_project_frontend(
    request: ProjectCreateRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Create a new SEO GPT Optimizer project (frontend-compatible endpoint).

    This endpoint creates a new project for organizing content generation
    and analysis work within the SEO GPT Optimizer. Returns data in the format
    expected by the frontend.
    """
    try:
        project_id = str(uuid.uuid4())

        # Create new project
        new_project = SEOGPTProject(
            project_id=project_id,
            title=request.title,
            topic=request.topic or request.title,
            target_language=request.target_language,
            content_type=request.content_type,
            target_gpt_rank_score=request.target_gpt_rank_score,
            status=ProjectStatus.DRAFT,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        db.add(new_project)
        db.commit()

        logger.info(f"✅ Created new SEO GPT project: {project_id}")

        return JSONResponse(
            status_code=201,
            content={
                "status": "success",
                "data": {
                    "project_id": project_id,
                    "project": {
                        "project_id": project_id,
                        "title": new_project.title,
                        "topic": new_project.topic,
                        "status": new_project.status.value,
                        "content_type": new_project.content_type.value,
                        "target_language": new_project.target_language,
                        "current_gpt_rank_score": new_project.current_gpt_rank_score,
                        "target_gpt_rank_score": new_project.target_gpt_rank_score,
                        "best_gpt_rank_score": new_project.best_gpt_rank_score,
                        "created_at": new_project.created_at.isoformat(),
                        "updated_at": new_project.updated_at.isoformat()
                    }
                }
            }
        )

    except Exception as e:
        logger.error(f"❌ Project creation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Project creation failed: {str(e)}")

# ============================================================================
# RESEARCH & ANALYSIS ENDPOINTS
# ============================================================================

@router.post("/research")
async def conduct_research(
    request: ResearchRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Conduct comprehensive research for a topic.
    
    This endpoint initiates a comprehensive research process including:
    - Search intent analysis
    - Google Top 10 + Reddit + Quora scraping
    - GPT reference responses
    - Entity extraction and common questions
    """
    try:
        logger.info(f"🔍 Starting research for topic: '{request.topic}'")
        
        # Conduct research
        research_results = await research_service.conduct_comprehensive_research(
            topic=request.topic,
            target_language=request.target_language,
            include_reddit=request.include_reddit,
            include_quora=request.include_quora,
            target_country=request.target_country or "ES",
            include_news=request.include_news or False
        )
        
        if research_results.get("status") == "error":
            raise HTTPException(status_code=500, detail=research_results.get("error_message"))
        
        # Store research results in database
        research_id = str(uuid.uuid4())

        # Get project if project_id is provided
        project_db_id = None
        if request.project_id:
            project = db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == request.project_id
            ).first()
            if project:
                project_db_id = project.id
                logger.info(f"🔗 Linking research to project: {request.project_id}")

        keyword_research = KeywordResearch(
            research_id=research_id,
            project_id=project_db_id,  # Link to project if provided
            topic=request.topic,
            target_language=request.target_language,
            research_type="comprehensive",
            intent_analysis=research_results.get("intent_analysis"),
            google_results=research_results.get("google_results"),
            social_insights=research_results.get("social_insights"),
            gpt_reference=research_results.get("gpt_reference"),
            entities_and_questions=research_results.get("entities_and_questions"),
            content_opportunities=research_results.get("content_opportunities"),
            research_confidence=research_results.get("research_summary", {}).get("research_confidence", 0.0),
            processing_time=research_results.get("processing_time", 0.0),
            total_sources_analyzed=len(research_results.get("google_results", {}).get("results", [])),
            status="completed"
        )
        
        db.add(keyword_research)
        db.commit()
        db.refresh(keyword_research)
        
        logger.info(f"✅ Research completed and stored for topic: '{request.topic}'")
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "research_id": research_id,
                "message": "Research completed successfully",
                **research_results
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Research failed for topic '{request.topic}': {str(e)}")
        raise HTTPException(status_code=500, detail=f"Research failed: {str(e)}")

# ============================================================================
# CONTENT GENERATION ENDPOINTS
# ============================================================================

@router.post("/content/generate-blog")
async def generate_blog_content(
    request: BlogGenerationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Generate complete blog content using AI content generation.

    This endpoint creates a full blog post including:
    - AI-generated content using Gemini/GPT-4
    - Contextual images with Ideogram
    - Real-time GPT Rank analysis
    - Structured blog format
    """
    try:
        logger.info(f"🎨 Generating blog content for project: {request.project_id}")

        # Get project
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == request.project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        logger.info(f"🧠 Using AI content generator to create blog content...")

        # Create content generation request
        ai_request = AIContentRequest(
            topic=request.topic,
            keywords=[request.topic],  # Use topic as primary keyword
            content_type="blog",
            tone="professional",
            length=request.target_length,
            target_audience="general audience",
            include_images=request.include_images,
            seo_optimized=True,
            saio_optimized=True
        )

        # Generate blog content using AI content generator
        generated_content = await ai_content_generator.generate_content(ai_request)

        logger.info(f"✅ Blog content generated! Word count: {generated_content.word_count}")

        # Use the generated content
        blog_content = generated_content.content

        # Generate images if requested
        image_urls = []
        if request.include_images:
            logger.info(f"🖼️ Generating {request.num_images} images for blog...")

            # Use the image prompts from AI content generator
            image_prompts = generated_content.image_prompts[:request.num_images]

            for i, image_data in enumerate(image_prompts):
                try:
                    # Create image prompt for blog section
                    image_prompt = await _create_blog_image_prompt_direct(
                        topic=request.topic,
                        image_purpose=image_data.get("prompt", f"Professional illustration for {request.topic}"),
                        image_index=i,
                        total_images=request.num_images
                    )

                    # Generate image with Ideogram
                    image_result = await ideogram_service.generate_image(
                        prompt=image_prompt,
                        aspect_ratio="16:9",
                        style_type="DESIGN"
                    )

                    if image_result and image_result.get("success"):
                        image_urls.append(image_result["image_url"])
                        logger.info(f"✅ Generated blog image {i+1}/{request.num_images}")
                    else:
                        image_urls.append(None)
                        logger.warning(f"❌ Failed to generate image {i+1}")

                except Exception as e:
                    logger.error(f"❌ Error generating image {i+1}: {e}")
                    image_urls.append(None)

        # Update project with generated content
        project.content_text = blog_content
        project.content_length = len(blog_content)
        project.word_count = len(blog_content.split())
        project.updated_at = datetime.utcnow()

        db.commit()

        logger.info(f"✅ Blog content generated successfully for project: {request.project_id}")

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "project_id": request.project_id,
                "message": "Blog content generated successfully",
                "data": {
                    "content": blog_content,
                    "title": generated_content.title,
                    "meta_description": generated_content.meta_description,
                    "images": image_urls,
                    "seo_analysis": {
                        "seo_score": generated_content.seo_score,
                        "saio_score": generated_content.saio_score,
                        "keywords": generated_content.keywords
                    },
                    "content_stats": {
                        "word_count": generated_content.word_count,
                        "character_count": len(blog_content),
                        "estimated_reading_time": f"{max(1, generated_content.word_count // 200)} min"
                    }
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Blog generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Blog generation failed: {str(e)}")

# ============================================================================
# CONTENT ANALYSIS ENDPOINTS
# ============================================================================

@router.post("/content/analyze")
async def analyze_content_seo(
    request: ContentAnalysisRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Analyze content for SEO and SAIO optimization.

    This endpoint provides real-time content analysis including:
    - SEO score and keyword analysis
    - SAIO optimization score
    - Readability analysis
    - Content structure analysis
    - Optimization suggestions
    """
    try:
        logger.info(f"🔍 Analyzing content for project: {request.project_id}")

        # Get project
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == request.project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Check if deep analysis is requested
        if request.deep_analysis:
            logger.info("🧠 Performing deep contextual analysis")
            contextual_results = await contextual_analyzer.analyze_content_deeply(
                content=request.content,
                target_keywords=request.target_keywords or []
            )

            # Return contextual analysis results
            return JSONResponse(
                status_code=200,
                content={
                    "status": "success",
                    "analysis_type": "contextual",
                    "gpt_rank_score": contextual_results['scores']['overall_score'],
                    "score_grade": "Excelente" if contextual_results['scores']['overall_score'] > 80 else "Bueno" if contextual_results['scores']['overall_score'] > 60 else "Mejorable",
                    "improvement_suggestions": contextual_results['suggestions'],
                    "content_stats": {
                        "word_count": contextual_results['content_analysis']['word_count'],
                        "paragraph_count": contextual_results['content_analysis']['paragraph_count'],
                        "sentence_count": contextual_results['content_analysis']['sentence_count']
                    },
                    "component_scores": {
                        "readability_score": contextual_results['scores']['readability_score'],
                        "structure_score": contextual_results['scores']['structure_score'],
                        "engagement_score": contextual_results['scores']['engagement_score'],
                        "clarity_score": contextual_results['scores']['readability_score']
                    },
                    "weaknesses_identified": len(contextual_results['weaknesses']),
                    "contextual_insights": {
                        "main_topic": contextual_results['content_analysis'].get('word_frequency', {}),
                        "readability_level": contextual_results['content_analysis'].get('readability_estimate', 'fair'),
                        "structure_quality": "good" if contextual_results['content_analysis']['has_headings'] else "needs_improvement"
                    }
                }
            )

        # Standard GPT Rank Engine analysis
        gpt_rank_results = await gpt_rank_service.analyze_content(
            content=request.content,
            target_keywords=request.target_keywords or [],
            content_type=request.content_type or "blog"
        )

        # Calculate additional metrics
        word_count = len(request.content.split())
        sentences = request.content.count('.') + request.content.count('!') + request.content.count('?')
        paragraphs = len([p for p in request.content.split('\n\n') if p.strip()])

        # Extract headings
        import re
        headings = []
        for match in re.finditer(r'^(#{1,6})\s+(.+)$', request.content, re.MULTILINE):
            level = len(match.group(1))
            text = match.group(2).strip()
            headings.append({"level": level, "text": text})

        # Calculate keyword density
        content_lower = request.content.lower()
        keyword_density = {}
        if request.target_keywords:
            for keyword in request.target_keywords:
                count = content_lower.count(keyword.lower())
                density = (count / word_count) * 100 if word_count > 0 else 0
                keyword_density[keyword] = round(density, 2)

        # Calculate readability score (simplified)
        avg_sentence_length = word_count / max(sentences, 1)
        readability_score = max(0, min(100, 100 - (avg_sentence_length - 15) * 2))

        # Calculate SAIO score
        saio_score = 70  # Base score
        if '?' in request.content:
            saio_score += 5  # Has questions
        if any(marker in request.content for marker in ['•', '-', '1.', '2.']):
            saio_score += 5  # Has lists
        if len(headings) >= 3:
            saio_score += 5  # Good structure
        if word_count >= 800:
            saio_score += 5  # Sufficient length
        if any(word in content_lower for word in ['cómo', 'qué', 'por qué', 'cuándo']):
            saio_score += 5  # Question-focused content

        saio_score = min(100, saio_score)

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "analysis": {
                    "score": gpt_rank_results.get("overall_score", 75),
                    "keywords": {
                        "primary": request.target_keywords[:3] if request.target_keywords else ["SEO", "contenido"],
                        "secondary": request.target_keywords[3:] if len(request.target_keywords or []) > 3 else ["marketing", "digital"],
                        "density": keyword_density
                    },
                    "readability": {
                        "score": int(readability_score),
                        "level": "Fácil" if readability_score > 80 else "Intermedio" if readability_score > 60 else "Difícil",
                        "suggestions": [
                            "Usa oraciones más cortas" if avg_sentence_length > 20 else "Longitud de oraciones adecuada",
                            "Añade más subtítulos" if len(headings) < 3 else "Estructura de títulos correcta"
                        ]
                    },
                    "structure": {
                        "headings": headings,
                        "paragraphs": paragraphs,
                        "wordCount": word_count,
                        "sentences": sentences
                    },
                    "saio": {
                        "score": saio_score,
                        "qAndA": '?' in request.content,
                        "lists": any(marker in request.content for marker in ['•', '-', '1.', '2.']),
                        "freshness": True,
                        "multimedia": 'img' in request.content.lower() or 'imagen' in request.content.lower(),
                        "sources": 'http' in request.content or 'fuente' in request.content.lower()
                    },
                    "suggestions": gpt_rank_results.get("suggestions", [
                        "Añade más palabras clave relevantes",
                        "Incluye preguntas frecuentes",
                        "Mejora la estructura con subtítulos",
                        "Añade elementos multimedia"
                    ])
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Content analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Content analysis failed: {str(e)}")

@router.post("/content/confidence-analysis")
async def analyze_content_confidence(
    request: ContentAnalysisRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Analyze content confidence for LLM ranking (Perplexity, ChatGPT, Claude, etc.)

    This endpoint is triggered manually when user clicks the LLM confidence analysis button.
    Evaluates how well content will rank in AI-powered search engines and LLMs.

    Returns:
    - LLM ranking confidence score with visual badge
    - LLM-specific weakness analysis
    - Improvement recommendations for AI systems
    - Priority optimizations for better LLM performance
    """
    try:
        logger.info(f"🧠 LLM confidence analysis requested for project: {request.project_id}")

        # Get project
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == request.project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Initialize contextual analyzer
        contextual_analyzer = ContextualSEOAnalyzer()

        # Perform LLM confidence analysis (user-triggered)
        confidence_results = contextual_analyzer.analyze_content_confidence(
            content=request.content,
            target_keywords=request.target_keywords or []
        )

        if confidence_results['status'] == 'error':
            raise HTTPException(status_code=500, detail="LLM confidence analysis failed")

        confidence_data = confidence_results['confidence_analysis']
        overall_confidence = confidence_data['overall_confidence']

        logger.info(f"✅ LLM confidence analysis completed - Score: {overall_confidence['overall_score']}")

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "analysis_type": "llm_ranking_confidence",
                "project_id": request.project_id,
                "overall_confidence": {
                    "score": overall_confidence['overall_score'],
                    "badge": overall_confidence['confidence_badge'],
                    "color": overall_confidence['confidence_color'],
                    "quality_level": overall_confidence['quality_level'],
                    "llm_performance": "Evaluación para Perplexity, ChatGPT, Claude y otros LLMs",
                    "summary": {
                        "total_weaknesses": overall_confidence['weaknesses_count'],
                        "high_priority_issues": overall_confidence['high_priority_issues'],
                        "improvement_opportunities": overall_confidence['improvement_opportunities']
                    }
                },
                "weaknesses_analysis": [
                    {
                        "type": weakness['type'],
                        "issue": weakness['issue'],
                        "severity": weakness['severity'],
                        "llm_impact": "Afecta ranking en sistemas de IA",
                        "confidence": {
                            "score": weakness['confidence_score'],
                            "badge": weakness['confidence_badge'],
                            "color": weakness['confidence_color']
                        },
                        "improvement_potential": weakness['improvement_potential'],
                        "current_value": weakness.get('current_value'),
                        "target_value": weakness.get('target_value')
                    }
                    for weakness in confidence_data['weaknesses_with_confidence']
                ],
                "quality_metrics": overall_confidence.get('quality_metrics', {}),
                "analysis_metadata": {
                    "timestamp": confidence_data['analysis_timestamp'],
                    "trigger_type": confidence_data['trigger_type'],
                    "analysis_type": confidence_data['analysis_type'],
                    "target_llms": ["Perplexity", "ChatGPT", "Claude", "Gemini"],
                    "content_length": len(request.content)
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ LLM confidence analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"LLM confidence analysis failed: {str(e)}")

@router.post("/content/analyze-saio")
async def analyze_saio_content(
    request: ContentAnalysisRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Analyze content with SAIO Agent - the world's first real SAIO optimizer.

    Based on comprehensive research:
    - 73% similarity between ChatGPT and Bing results
    - 100% of Perplexity citations have images
    - 90% of YMYL citations are lists/how-tos
    - E-E-A-T crucial for AI trust
    """
    try:
        logger.info(f"🧠 SAIO Agent analyzing content for project: {request.project_id}")

        # Get project
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == request.project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Analyze with SAIO Agent
        saio_results = await saio_agent_service.analyze_saio_score(
            content=request.content,
            title=project.title,
            url=""
        )

        if "error" in saio_results:
            raise HTTPException(status_code=500, detail=saio_results["error"])

        logger.info(f"✅ SAIO analysis completed - Score: {saio_results['saio_score']}")

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "project_id": request.project_id,
                "message": "SAIO analysis completed",
                "saio_results": saio_results,
                "analysis_type": "research_based_saio",
                "world_first": True
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ SAIO analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"SAIO analysis failed: {str(e)}")

# ============================================================================
# SAIO (SEARCH AI OPTIMIZATION) ENDPOINTS
# ============================================================================

@router.post("/content/generate-saio")
async def generate_saio_content(
    request: SAIOContentRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Generate SAIO-optimized content - World's first real SAIO content generator.

    Uses research-backed templates and optimization strategies for:
    - Google SGE
    - ChatGPT
    - Perplexity
    - Bing Chat
    """
    try:
        logger.info(f"🚀 Generating SAIO content for project: {request.project_id}")

        # Get project
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == request.project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Generate SAIO-optimized content
        saio_content = await saio_agent_service.generate_saio_optimized_content(
            topic=request.topic,
            content_type=request.content_type
        )

        if "error" in saio_content:
            raise HTTPException(status_code=500, detail=saio_content["error"])

        # Update project with generated content
        project.content_text = saio_content["content"]
        project.content_length = len(saio_content["content"])
        project.word_count = len(saio_content["content"].split())
        project.updated_at = datetime.utcnow()

        db.commit()

        logger.info(f"✅ SAIO content generated - Score: {saio_content['saio_analysis']['saio_score']}")

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "project_id": request.project_id,
                "message": "SAIO content generated successfully",
                "data": {
                    "content": saio_content["content"],
                    "saio_analysis": saio_content["saio_analysis"],
                    "template_used": saio_content["template_used"],
                    "optimization_targets": request.optimize_for,
                    "world_first": True,
                    "research_based": True
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ SAIO content generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"SAIO content generation failed: {str(e)}")

# ============================================================================
# PROJECT MANAGEMENT & ANALYTICS ENDPOINTS
# ============================================================================

@router.get("/projects/{project_id}/score")
async def get_project_score(
    project_id: str,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get current GPT Rank Score for a project."""
    try:
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()
        
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get latest analysis
        latest_analysis = db.query(ContentAnalysis).filter(
            ContentAnalysis.project_id == project.id
        ).order_by(ContentAnalysis.created_at.desc()).first()
        
        # Get score history
        score_history = db.query(GPTRankHistory).filter(
            GPTRankHistory.project_id == project.id
        ).order_by(GPTRankHistory.created_at.desc()).limit(10).all()
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "project_id": project_id,
                "current_score": project.current_gpt_rank_score,
                "best_score": project.best_gpt_rank_score,
                "target_score": project.target_gpt_rank_score,
                "score_grade": latest_analysis.score_grade if latest_analysis else "N/A",
                "confidence_level": latest_analysis.confidence_level if latest_analysis else "unknown",
                "latest_analysis": {
                    "analysis_id": latest_analysis.analysis_id,
                    "created_at": latest_analysis.created_at.isoformat(),
                    "component_scores": {
                        "semantic_similarity": latest_analysis.semantic_similarity_score,
                        "logical_coherence": latest_analysis.logical_coherence_score,
                        "authority_signals": latest_analysis.authority_signals_score,
                        "citability_score": latest_analysis.citability_score,
                        "clarity_score": latest_analysis.clarity_score,
                        "completeness_score": latest_analysis.completeness_score
                    }
                } if latest_analysis else None,
                "score_history": [
                    {
                        "score": history.gpt_rank_score,
                        "change": history.score_change,
                        "grade": history.score_grade,
                        "created_at": history.created_at.isoformat()
                    }
                    for history in score_history
                ]
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get project score: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get project score: {str(e)}")

@router.get("/projects/{project_id}")
async def get_project(
    project_id: str,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get a specific SEO GPT Optimizer project."""
    try:
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": {
                    "project_id": project.project_id,
                    "title": project.title,
                    "topic": project.topic,
                    "status": project.status.value,
                    "content_type": project.content_type.value,
                    "target_language": project.target_language,
                    "current_gpt_rank_score": project.current_gpt_rank_score,
                    "target_gpt_rank_score": project.target_gpt_rank_score,
                    "best_gpt_rank_score": project.best_gpt_rank_score,
                    "content_text": project.content_text,
                    "content_length": project.content_length,
                    "word_count": project.word_count,
                    "created_at": project.created_at.isoformat(),
                    "updated_at": project.updated_at.isoformat()
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get project: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get project: {str(e)}")

@router.patch("/projects/{project_id}")
async def update_project(
    project_id: str,
    updates: dict,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Update a specific SEO GPT Optimizer project."""
    try:
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Update allowed fields
        allowed_fields = [
            'title', 'topic', 'content_text', 'content_length', 'word_count',
            'current_gpt_rank_score', 'status', 'target_gpt_rank_score'
        ]

        for field, value in updates.items():
            if field in allowed_fields and hasattr(project, field):
                setattr(project, field, value)

        project.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(project)

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": {
                    "project_id": project.project_id,
                    "title": project.title,
                    "topic": project.topic,
                    "status": project.status.value,
                    "content_type": project.content_type.value,
                    "target_language": project.target_language,
                    "current_gpt_rank_score": project.current_gpt_rank_score,
                    "target_gpt_rank_score": project.target_gpt_rank_score,
                    "best_gpt_rank_score": project.best_gpt_rank_score,
                    "content_text": project.content_text,
                    "content_length": project.content_length,
                    "word_count": project.word_count,
                    "created_at": project.created_at.isoformat(),
                    "updated_at": project.updated_at.isoformat()
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to update project: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update project: {str(e)}")

@router.delete("/projects/{project_id}")
async def delete_project(
    project_id: str,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Delete a specific SEO GPT Optimizer project."""
    try:
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Delete related records first to avoid foreign key constraints
        # Note: The database schema uses INTEGER foreign keys to seo_gpt_projects.id
        # but the models define them as STRING. We need to use the internal ID.
        try:
            project_internal_id = project.id  # This is the INTEGER primary key

            # Delete project contents (using internal integer ID)
            from app.db.seo_gpt_models import ProjectContent
            db.query(ProjectContent).filter(
                ProjectContent.project_id == project_internal_id
            ).delete(synchronize_session=False)

            # Delete content analyses (using internal integer ID)
            from app.db.seo_gpt_models import ContentAnalysis
            db.query(ContentAnalysis).filter(
                ContentAnalysis.project_id == project_internal_id
            ).delete(synchronize_session=False)

            # Delete GPT rank history (using internal integer ID)
            from app.db.seo_gpt_models import GPTRankHistory
            db.query(GPTRankHistory).filter(
                GPTRankHistory.project_id == project_internal_id
            ).delete(synchronize_session=False)

            # Delete keyword research (using internal integer ID)
            from app.db.seo_gpt_models import KeywordResearch
            db.query(KeywordResearch).filter(
                KeywordResearch.project_id == project_internal_id
            ).delete(synchronize_session=False)

            # Now delete the project
            db.delete(project)
            db.commit()

            logger.info(f"✅ Successfully deleted project: {project_id} (internal ID: {project_internal_id})")

        except Exception as delete_error:
            db.rollback()
            logger.error(f"❌ Error during project deletion: {str(delete_error)}")
            raise delete_error

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "message": "Project deleted successfully"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to delete project: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete project: {str(e)}")

@router.get("/projects")
async def list_projects(
    user_id: Optional[str] = None,
    status: Optional[ProjectStatus] = None,
    limit: int = 20,
    offset: int = 0,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """List SEO GPT Optimizer projects."""
    try:
        query = db.query(SEOGPTProject)
        
        if user_id:
            query = query.filter(SEOGPTProject.user_id == user_id)
        
        if status:
            query = query.filter(SEOGPTProject.status == status)
        
        projects = query.order_by(SEOGPTProject.updated_at.desc()).offset(offset).limit(limit).all()
        
        project_list = []
        for project in projects:
            project_list.append({
                "project_id": project.project_id,
                "title": project.title,
                "topic": project.topic,
                "status": project.status.value,
                "content_type": project.content_type.value,
                "current_gpt_rank_score": project.current_gpt_rank_score,
                "target_gpt_rank_score": project.target_gpt_rank_score,
                "best_gpt_rank_score": project.best_gpt_rank_score,
                "word_count": project.word_count,
                "created_at": project.created_at.isoformat(),
                "updated_at": project.updated_at.isoformat()
            })
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "projects": project_list,
                "total": len(project_list),
                "limit": limit,
                "offset": offset
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to list projects: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list projects: {str(e)}")

@router.get("/dashboard/stats")
async def get_dashboard_stats(
    user_id: Optional[str] = None,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get dashboard analytics statistics."""
    try:
        # Base query
        query = db.query(SEOGPTProject)
        if user_id:
            query = query.filter(SEOGPTProject.user_id == user_id)

        # Total projects
        total_projects = query.count()

        # Active projects (not draft or completed)
        active_projects = query.filter(
            SEOGPTProject.status.in_([ProjectStatus.RESEARCHING, ProjectStatus.WRITING, ProjectStatus.OPTIMIZING])
        ).count()

        # Average scores
        avg_current_score = db.query(func.avg(SEOGPTProject.current_gpt_rank_score)).filter(
            SEOGPTProject.current_gpt_rank_score > 0
        ).scalar() or 0.0

        # Recent activity (last 30 days)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        recent_projects = query.filter(SEOGPTProject.created_at >= thirty_days_ago).count()

        # Total research conducted
        total_research = db.query(KeywordResearch).count()

        # Score improvements this month
        score_improvements = db.query(GPTRankHistory).filter(
            GPTRankHistory.created_at >= thirty_days_ago,
            GPTRankHistory.score_change > 0
        ).count()

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": {
                    "total_projects": total_projects,
                    "active_projects": active_projects,
                    "avg_gpt_rank_score": round(avg_current_score, 2),
                    "total_research_conducted": total_research,
                    "improvement_this_month": score_improvements
                }
            }
        )

    except Exception as e:
        logger.error(f"❌ Failed to get dashboard stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard stats: {str(e)}")

@router.get("/analytics/score-trends")
async def get_score_trends(
    user_id: Optional[str] = None,
    days: int = 30,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get score trends over time."""
    try:
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        # Get score history
        query = db.query(GPTRankHistory).filter(
            GPTRankHistory.created_at >= start_date,
            GPTRankHistory.created_at <= end_date
        )

        if user_id:
            # Join with projects to filter by user
            query = query.join(SEOGPTProject).filter(SEOGPTProject.user_id == user_id)

        score_history = query.order_by(GPTRankHistory.created_at.asc()).all()

        # Group by date
        daily_scores = {}
        for history in score_history:
            date_key = history.created_at.date().isoformat()
            if date_key not in daily_scores:
                daily_scores[date_key] = {
                    "date": date_key,
                    "scores": [],
                    "avg_score": 0.0,
                    "improvements": 0,
                    "total_analyses": 0
                }

            daily_scores[date_key]["scores"].append(history.gpt_rank_score)
            daily_scores[date_key]["total_analyses"] += 1
            if history.score_change > 0:
                daily_scores[date_key]["improvements"] += 1

        # Calculate averages
        trends = []
        for date_data in daily_scores.values():
            date_data["avg_score"] = round(sum(date_data["scores"]) / len(date_data["scores"]), 2)
            del date_data["scores"]  # Remove raw scores to reduce response size
            trends.append(date_data)

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "score_trends": sorted(trends, key=lambda x: x["date"]),
                "period_days": days,
                "total_data_points": len(trends)
            }
        )

    except Exception as e:
        logger.error(f"❌ Failed to get score trends: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get score trends: {str(e)}")

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

async def download_image_locally(image_url: str, project_id: str) -> str:
    """
    Download an image from Ideogram to local storage and return the local URL.

    Args:
        image_url: The original Ideogram image URL
        project_id: Project ID for organizing files

    Returns:
        Local URL path to the downloaded image
    """
    try:
        # Create images directory if it doesn't exist
        images_dir = "static/images/generated"
        os.makedirs(images_dir, exist_ok=True)

        # Generate unique filename
        image_id = str(uuid.uuid4())
        filename = f"{project_id}_{image_id}.png"
        local_path = os.path.join(images_dir, filename)

        # Download the image
        async with aiohttp.ClientSession() as session:
            async with session.get(image_url) as response:
                if response.status == 200:
                    async with aiofiles.open(local_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            await f.write(chunk)

                    # Return the local URL with full backend URL
                    local_url = f"http://localhost:8000/static/images/generated/{filename}"
                    logger.info(f"✅ Image downloaded locally: {local_url}")
                    return local_url
                else:
                    logger.error(f"❌ Failed to download image: HTTP {response.status}")
                    return image_url  # Fallback to original URL

    except Exception as e:
        logger.error(f"❌ Error downloading image locally: {str(e)}")
        return image_url  # Fallback to original URL
async def _create_blog_image_prompt_direct(topic: str, image_purpose: str, image_index: int, total_images: int) -> str:
    """Create contextual image prompt for blog sections without Creative Genius."""

    # Define image purposes based on position
    image_purposes = [
        "Hero image - main concept visualization",
        "Supporting concept illustration",
        "Data or process visualization",
        "Conclusion or call-to-action visual"
    ]

    # Get purpose for this image
    purpose_index = min(image_index, len(image_purposes) - 1)
    purpose = image_purposes[purpose_index]

    # Create contextual prompt
    base_prompt = f"""
Professional illustration for blog article about {topic}.

Purpose: {purpose}
Style: Modern, clean, professional design
Context: Blog article illustration
Art direction: Minimalist, high-quality, suitable for professional content

High quality, 16:9 aspect ratio, suitable for blog content.
Text overlay friendly, clean background, professional aesthetic.
"""

    return base_prompt.strip()



def _generate_fallback_blog_content(topic: str) -> str:
    """Generate fallback blog content when AI is not available."""
    return f"""
# {topic}

## Introducción

En este artículo exploraremos todo lo que necesitas saber sobre {topic}.

## ¿Qué es {topic}?

[Contenido generado automáticamente - Se recomienda editar manualmente]

## Beneficios principales

1. Primer beneficio importante
2. Segundo beneficio clave
3. Tercer beneficio relevante

## Cómo implementar

Pasos básicos para comenzar:

1. Primer paso
2. Segundo paso
3. Tercer paso

## Conclusión

{topic} es un tema importante que merece atención. Con la información correcta, puedes aprovechar al máximo sus beneficios.

## ¿Listo para comenzar?

Comienza hoy mismo aplicando estos conceptos en tu día a día.
"""

# ============================================================================
# SEO IMAGE GENERATION ENDPOINTS
# ============================================================================

@router.post("/content/generate-seo-image")
async def generate_seo_image(
    request: SEOImageGenerationRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Generate SEO-optimized images using Ideogram AI.

    This endpoint creates images specifically optimized for SEO content:
    - Contextually relevant to the content and keywords
    - Professional quality suitable for web use
    - Optimized prompts for better search visibility
    - Automatic alt text generation for accessibility
    """
    try:
        logger.info(f"🎨 Generating SEO image for project: {request.project_id}")

        # For demo purposes, allow generation without requiring existing project
        # In production, you might want to validate the project exists
        if request.project_id != "demo-project":
            # Get project for non-demo requests
            project = db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == request.project_id
            ).first()

            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

        # Enhance prompt for SEO optimization
        enhanced_prompt = f"{request.prompt}, estilo {request.style}, profesional, alta calidad, sin texto"

        # Add keyword context if provided
        if request.keywords:
            keyword_context = ", ".join(request.keywords[:3])  # Top 3 keywords
            enhanced_prompt += f", relacionado con {keyword_context}"

        logger.info(f"🎨 Enhanced prompt: {enhanced_prompt}")

        # Generate image with Ideogram using the correct method
        # Convert aspect_ratio to dimensions for Ideogram API
        dimensions = None
        if request.aspect_ratio == "16:9":
            dimensions = {"width": 1792, "height": 1024}
        elif request.aspect_ratio == "9:16":
            dimensions = {"width": 1024, "height": 1792}
        else:  # 1:1 or default
            dimensions = {"width": 1024, "height": 1024}

        image_result = await ideogram_service.generate_image(
            prompt=enhanced_prompt,
            dimensions=dimensions
        )

        if not image_result or not image_result.get("success"):
            raise HTTPException(
                status_code=500,
                detail="Failed to generate image with Ideogram API"
            )

        # Generate SEO-optimized alt text if not provided
        alt_text = request.alt_text
        if not alt_text and request.keywords:
            primary_keyword = request.keywords[0] if request.keywords else "imagen"
            alt_text = f"{primary_keyword.title()} - Imagen profesional para contenido SEO"
        elif not alt_text:
            alt_text = f"Imagen profesional - {request.prompt[:50]}"

        # Download image locally for better performance and CORS handling
        original_image_url = image_result["image_url"]
        logger.info(f"📥 Downloading image locally from: {original_image_url}")

        try:
            local_image_url = await download_image_locally(original_image_url, request.project_id)
            logger.info(f"✅ Image downloaded locally: {local_image_url}")
        except Exception as download_error:
            logger.warning(f"⚠️ Failed to download image locally: {download_error}")
            local_image_url = original_image_url  # Fallback to original URL

        # Prepare response
        response_data = {
            "success": True,
            "image_url": local_image_url,  # Return local URL for better performance
            "original_url": original_image_url,  # Keep original for reference
            "prompt_used": enhanced_prompt,
            "alt_text": alt_text,
            "seo_keywords": request.keywords,
            "style": request.style,
            "aspect_ratio": request.aspect_ratio,
            "project_id": request.project_id
        }

        logger.info(f"✅ SEO image generated successfully: {local_image_url}")

        return JSONResponse(
            status_code=200,
            content=response_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ SEO image generation failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"SEO image generation failed: {str(e)}"
        )


@router.get("/content/proxy-image")
async def proxy_image(url: str):
    """
    Proxy endpoint to serve images from external sources (like Ideogram)
    This solves CORS issues when the frontend tries to access external image URLs
    """
    try:
        logger.info(f"🖼️ Proxying image from: {url}")

        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    content = await response.read()
                    content_type = response.headers.get('content-type', 'image/png')

                    logger.info(f"✅ Image proxied successfully, content-type: {content_type}")

                    return Response(
                        content=content,
                        media_type=content_type,
                        headers={
                            "Access-Control-Allow-Origin": "*",
                            "Access-Control-Allow-Methods": "GET",
                            "Access-Control-Allow-Headers": "*",
                            "Cache-Control": "public, max-age=3600"
                        }
                    )
                else:
                    logger.error(f"❌ Failed to fetch image: HTTP {response.status}")
                    raise HTTPException(status_code=response.status, detail="Failed to fetch image")

    except Exception as e:
        logger.error(f"❌ Image proxy failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Image proxy failed: {str(e)}")


# ============================================================================
# PROJECT CONTENT MANAGEMENT ENDPOINTS
# ============================================================================

@router.get("/projects/{project_id}/contents")
async def get_project_contents(
    project_id: str,
    content_type: Optional[str] = None,
    status: Optional[str] = None,
    limit: int = 20,
    offset: int = 0,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get all content items for a specific project."""
    try:
        # Verify project exists
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Build query
        query = db.query(ProjectContent).filter(ProjectContent.project_id == project_id)

        # Apply filters
        if content_type:
            query = query.filter(ProjectContent.content_type == content_type)
        if status:
            query = query.filter(ProjectContent.status == status)

        # Get total count
        total = query.count()

        # Apply pagination and ordering
        contents = query.order_by(ProjectContent.updated_at.desc()).offset(offset).limit(limit).all()

        # Format response
        content_list = []
        for content in contents:
            content_list.append({
                "content_id": content.content_id,
                "title": content.title,
                "content_type": content.content_type,
                "status": content.status,
                "content_text": content.content_text,  # ADD MISSING CONTENT TEXT
                "content_html": content.content_html,  # ADD MISSING CONTENT HTML
                "word_count": content.word_count,
                "content_length": content.content_length,
                "current_gpt_rank_score": content.current_gpt_rank_score,
                "target_gpt_rank_score": content.target_gpt_rank_score,
                "seo_score": content.seo_score,
                "target_keywords": content.target_keywords_list,
                "meta_description": content.meta_description,
                "slug": content.slug,
                "created_at": content.created_at.isoformat(),
                "updated_at": content.updated_at.isoformat(),
                "published_at": content.published_at.isoformat() if content.published_at else None
            })

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": {
                    "contents": content_list,
                    "total": total,
                    "limit": limit,
                    "offset": offset
                }
            }
        )

    except Exception as e:
        logger.error(f"❌ Error getting project contents for {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting project contents: {str(e)}")


@router.post("/projects/{project_id}/contents")
async def create_project_content(
    project_id: str,
    request: ProjectContentCreateRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Create a new content item within a project."""
    try:
        # Verify project exists
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Generate content ID
        content_id = str(uuid.uuid4())

        # Calculate word count and content length
        content_text = request.content_text or ""
        word_count = len(content_text.strip().split()) if content_text.strip() else 0
        content_length = len(content_text)

        # Generate slug if not provided
        slug = request.slug
        if not slug and request.title:
            import re
            slug = re.sub(r'[^a-zA-Z0-9\s-]', '', request.title.lower())
            slug = re.sub(r'\s+', '-', slug).strip('-')

        # Create new content
        new_content = ProjectContent(
            content_id=content_id,
            project_id=project_id,  # Use string project_id directly
            title=request.title,
            content_type=request.content_type,
            content_text=content_text,
            content_html=request.content_html,
            word_count=word_count,
            content_length=content_length,
            meta_description=request.meta_description,
            slug=slug,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        # Set target keywords
        if request.target_keywords:
            new_content.target_keywords_list = request.target_keywords

        db.add(new_content)
        db.commit()
        db.refresh(new_content)

        logger.info(f"✅ Created new content: {content_id} in project: {project_id}")

        return JSONResponse(
            status_code=201,
            content={
                "status": "success",
                "data": {
                    "content_id": content_id,
                    "title": new_content.title,
                    "content_type": new_content.content_type,
                    "status": new_content.status,
                    "word_count": new_content.word_count,
                    "created_at": new_content.created_at.isoformat()
                },
                "message": "Content created successfully"
            }
        )

    except Exception as e:
        logger.error(f"❌ Error creating content in project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating content: {str(e)}")


@router.get("/projects/{project_id}/contents/{content_id}")
async def get_project_content(
    project_id: str,
    content_id: str,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get a specific content item from a project."""
    try:
        # Verify project exists
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Get content
        content = db.query(ProjectContent).filter(
            ProjectContent.content_id == content_id,
            ProjectContent.project_id == project_id  # Use string project_id directly
        ).first()

        if not content:
            raise HTTPException(status_code=404, detail="Content not found")

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": {
                    "content_id": content.content_id,
                    "title": content.title,
                    "content_type": content.content_type,
                    "status": content.status,
                    "content_text": content.content_text,
                    "content_html": content.content_html,
                    "word_count": content.word_count,
                    "content_length": content.content_length,
                    "current_gpt_rank_score": content.current_gpt_rank_score,
                    "target_gpt_rank_score": content.target_gpt_rank_score,
                    "seo_score": content.seo_score,
                    "target_keywords": content.target_keywords_list,
                    "meta_description": content.meta_description,
                    "slug": content.slug,
                    "created_at": content.created_at.isoformat(),
                    "updated_at": content.updated_at.isoformat(),
                    "published_at": content.published_at.isoformat() if content.published_at else None
                }
            }
        )

    except Exception as e:
        logger.error(f"❌ Error getting content {content_id} from project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting content: {str(e)}")


@router.patch("/projects/{project_id}/contents/{content_id}")
async def update_project_content(
    project_id: str,
    content_id: str,
    request: ProjectContentUpdateRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Update a specific content item in a project."""
    try:
        # 🔍 COMPREHENSIVE BACKEND DEBUGGING
        logger.info(f"🔄 PATCH REQUEST - Starting update process")
        logger.info(f"🔄 PATCH REQUEST - project_id: {project_id}")
        logger.info(f"🔄 PATCH REQUEST - content_id: {content_id}")
        logger.info(f"🔄 PATCH REQUEST - request data: {request.model_dump()}")
        logger.info(f"🔄 PATCH REQUEST - request title: {request.title}")
        logger.info(f"🔄 PATCH REQUEST - request content_text: {request.content_text[:100] if request.content_text else 'None'}...")
        logger.info(f"🔄 PATCH REQUEST - request content_html: {request.content_html[:100] if request.content_html else 'None'}...")
        logger.info(f"🔄 PATCH REQUEST - content_text length: {len(request.content_text or '')}")
        logger.info(f"🔄 PATCH REQUEST - content_html length: {len(request.content_html or '')}")

        # Verify project exists
        logger.info(f"🔍 DATABASE - Verifying project exists...")
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()

        if not project:
            logger.error(f"❌ PROJECT NOT FOUND - project_id: {project_id}")
            raise HTTPException(status_code=404, detail="Project not found")

        logger.info(f"✅ PROJECT FOUND - project_id: {project.project_id}, title: {project.title}")

        # Get content
        logger.info(f"🔍 DATABASE - Searching for content...")
        logger.info(f"🔍 DATABASE - Query filters: content_id={content_id}, project_id={project_id}")
        content = db.query(ProjectContent).filter(
            ProjectContent.content_id == content_id,
            ProjectContent.project_id == project_id  # Use string project_id directly
        ).first()

        if not content:
            logger.error(f"❌ CONTENT NOT FOUND - content_id: {content_id}, project_id: {project_id}")
            # Try to find content by ID only to see if it exists
            content_by_id_only = db.query(ProjectContent).filter(ProjectContent.content_id == content_id).first()
            if content_by_id_only:
                logger.error(f"❌ CONTENT EXISTS but project_id mismatch: found project_id={content_by_id_only.project_id}, expected={project_id}")
            else:
                logger.error(f"❌ CONTENT DOES NOT EXIST with content_id: {content_id}")
            raise HTTPException(status_code=404, detail="Content not found")

        logger.info(f"✅ CONTENT FOUND - title: {content.title}")
        logger.info(f"✅ CONTENT FOUND - current content_text length: {len(content.content_text or '')}")
        logger.info(f"✅ CONTENT FOUND - current content_html length: {len(content.content_html or '')}")



        # Update fields
        if request.title is not None:
            content.title = request.title
        if request.content_text is not None:
            content.content_text = request.content_text
            content.word_count = len(request.content_text.strip().split()) if request.content_text.strip() else 0
            content.content_length = len(request.content_text)
        if request.content_html is not None:
            content.content_html = request.content_html
        if request.status is not None:
            content.status = request.status
            if request.status == "published" and not content.published_at:
                content.published_at = datetime.utcnow()
        if request.target_keywords is not None:
            content.target_keywords_list = request.target_keywords
        if request.meta_description is not None:
            content.meta_description = request.meta_description
        if request.slug is not None:
            content.slug = request.slug
        if request.current_gpt_rank_score is not None:
            content.current_gpt_rank_score = request.current_gpt_rank_score
        if request.seo_score is not None:
            content.seo_score = request.seo_score

        content.updated_at = datetime.utcnow()

        # 🔍 LOG BEFORE COMMIT
        logger.info(f"🔄 DATABASE - Before commit:")
        logger.info(f"   - title: {content.title}")
        logger.info(f"   - content_text length: {len(content.content_text or '')}")
        logger.info(f"   - content_html length: {len(content.content_html or '')}")
        logger.info(f"   - word_count: {content.word_count}")

        logger.info(f"🔄 DATABASE - Committing changes...")
        db.commit()

        logger.info(f"🔄 DATABASE - Refreshing content...")
        db.refresh(content)

        # 🔍 LOG AFTER COMMIT
        logger.info(f"✅ DATABASE - After commit:")
        logger.info(f"   - title: {content.title}")
        logger.info(f"   - content_text length: {len(content.content_text or '')}")
        logger.info(f"   - content_html length: {len(content.content_html or '')}")
        logger.info(f"   - word_count: {content.word_count}")

        logger.info(f"✅ Updated content: {content_id} in project: {project_id}")

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": {
                    "content_id": content.content_id,
                    "title": content.title,
                    "content_type": content.content_type,
                    "status": content.status,
                    "content_text": content.content_text,
                    "content_html": content.content_html,
                    "word_count": content.word_count,
                    "content_length": content.content_length,
                    "current_gpt_rank_score": content.current_gpt_rank_score,
                    "target_gpt_rank_score": content.target_gpt_rank_score,
                    "seo_score": content.seo_score,
                    "target_keywords": content.target_keywords,
                    "meta_description": content.meta_description,
                    "slug": content.slug,
                    "created_at": content.created_at.isoformat(),
                    "updated_at": content.updated_at.isoformat(),
                    "published_at": content.published_at.isoformat() if content.published_at else None
                },
                "message": "Content updated successfully"
            }
        )

    except Exception as e:
        logger.error(f"❌ Error updating content {content_id} in project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating content: {str(e)}")


@router.get("/projects/{project_id}/research")
async def get_project_research(
    project_id: str,
    limit: int = 20,
    offset: int = 0,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get all research items for a specific project."""
    try:
        # Verify project exists
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Get research
        research_query = db.query(KeywordResearch).filter(
            KeywordResearch.project_id == project.id
        )

        total = research_query.count()
        research_items = research_query.order_by(KeywordResearch.created_at.desc()).offset(offset).limit(limit).all()

        # Format response
        research_list = []
        for research in research_items:
            research_list.append({
                "research_id": research.research_id,
                "topic": research.topic,
                "target_language": research.target_language,
                "research_type": research.research_type,
                "research_confidence": research.research_confidence,
                "processing_time": research.processing_time,
                "total_sources_analyzed": research.total_sources_analyzed,
                "status": research.status,
                "created_at": research.created_at.isoformat()
            })

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": {
                    "research": research_list,
                    "total": total,
                    "limit": limit,
                    "offset": offset
                }
            }
        )

    except Exception as e:
        logger.error(f"❌ Error getting research for project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting project research: {str(e)}")

@router.delete("/projects/{project_id}/contents/{content_id}", status_code=204)
async def delete_project_content(
    project_id: str,
    content_id: str,
    db: Session = Depends(get_db)
):
    """Delete a content item from a project."""
    try:
        # Verify project exists
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Find and delete content
        content = db.query(ProjectContent).filter(
            ProjectContent.project_id == project_id,
            ProjectContent.content_id == content_id
        ).first()

        if not content:
            # Content doesn't exist - consider this a successful deletion
            logger.info(f"⚠️ Content {content_id} not found in project {project_id} - already deleted")
            return Response(status_code=204)

        db.delete(content)
        db.commit()

        logger.info(f"✅ Deleted content: {content_id} from project: {project_id}")

        # Return 204 No Content (successful deletion)
        return Response(status_code=204)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error deleting content: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting content: {str(e)}")
