"""
API endpoints for Runway ML image and video generation.
"""

import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse, StreamingResponse
import httpx
import io

from app.schemas.runway import (
    TextToImageRequest,
    ImageToVideoRequest,
    RunwayWorkflowRequest,
    ImageGenerationResponse,
    VideoGenerationResponse,
    RunwayWorkflowResponse,
    MediaDownloadRequest,
    ImageToVideoOnlyRequest
)
from app.services.runway_service import runway_service
from app.core.config import settings

logger = logging.getLogger(__name__)
router = APIRouter()

async def verify_runway_api_key():
    """Verify Runway API key is configured."""
    if not settings.RUNWAY_API_KEY:
        logger.error("Runway API key not configured")
        raise HTTPException(
            status_code=500,
            detail="Runway ML API key not configured"
        )

@router.post("/text-to-image", response_model=ImageGenerationResponse)
async def generate_image(
    request: TextToImageRequest,
    _: None = Depends(verify_runway_api_key)
) -> ImageGenerationResponse:
    """
    Generate an image from text using Runway's gen4_image model.
    
    This endpoint creates high-quality images from text descriptions using
    Runway ML's advanced image generation model.
    """
    try:
        logger.info(f"Received image generation request: {request.prompt_text[:100]}...")
        
        # Convert reference images to dict format if provided
        reference_images = None
        if request.reference_images:
            reference_images = [
                {"uri": img.uri, "tag": img.tag}
                for img in request.reference_images
            ]
        
        # Generate image
        result = await runway_service.generate_image(
            prompt_text=request.prompt_text,
            ratio=request.ratio,
            seed=request.seed,
            reference_images=reference_images
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=500,
                detail=f"Image generation failed: {result.get('error', 'Unknown error')}"
            )
        
        return ImageGenerationResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in image generation endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.post("/image-to-video", response_model=VideoGenerationResponse)
async def generate_video(
    request: ImageToVideoRequest,
    _: None = Depends(verify_runway_api_key)
) -> VideoGenerationResponse:
    """
    Convert an image to video using Runway's gen4_turbo model.
    
    This endpoint takes an image (URL or data URI) and converts it into
    a short video with motion and effects.
    """
    try:
        logger.info(f"Received video generation request: {request.prompt_text[:100]}...")
        
        # Generate video
        result = await runway_service.image_to_video(
            prompt_image=request.prompt_image,
            prompt_text=request.prompt_text,
            ratio=request.ratio,
            duration=request.duration
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=500,
                detail=f"Video generation failed: {result.get('error', 'Unknown error')}"
            )
        
        return VideoGenerationResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in video generation endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.post("/image-to-video-only", response_model=VideoGenerationResponse)
async def generate_video_only(
    request: ImageToVideoOnlyRequest,
    _: None = Depends(verify_runway_api_key)
) -> VideoGenerationResponse:
    """
    Convert an image to video using Runway ML (standalone operation).

    This endpoint is specifically for the "Solo Video" tab, allowing users
    to upload an image and convert it directly to video without generating
    the image first.
    """
    try:
        logger.info(f"Received standalone video generation request: {request.prompt_text[:100]}...")

        # Validate model and ratio compatibility
        if request.model == "gen3a_turbo":
            valid_ratios = ["1280:768", "768:1280"]
            if request.ratio not in valid_ratios:
                request.ratio = "1280:768"  # Default for gen3a_turbo
        elif request.model == "gen4_turbo":
            valid_ratios = ["1280:720", "720:1280", "1104:832", "832:1104", "960:960", "1584:672"]
            if request.ratio not in valid_ratios:
                request.ratio = "1280:720"  # Default for gen4_turbo

        # Validate duration
        if request.duration not in [5, 10]:
            request.duration = 10  # Default

        # Generate video
        result = await runway_service.image_to_video(
            prompt_image=request.prompt_image,
            prompt_text=request.prompt_text,
            ratio=request.ratio,
            duration=request.duration
        )

        if not result["success"]:
            raise HTTPException(
                status_code=500,
                detail=f"Video generation failed: {result.get('error', 'Unknown error')}"
            )

        return VideoGenerationResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in standalone video generation endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.post("/workflow", response_model=RunwayWorkflowResponse)
async def complete_workflow(
    request: RunwayWorkflowRequest,
    _: None = Depends(verify_runway_api_key)
) -> RunwayWorkflowResponse:
    """
    Complete workflow: Generate image from text, then convert to video.
    
    This endpoint combines both operations:
    1. Generate an image from the text prompt
    2. Convert that image to a video
    
    This is the main workflow for creating videos from text descriptions.
    """
    try:
        logger.info(f"Received workflow request: {request.prompt_text[:100]}...")
        
        # Step 1: Generate image
        logger.info("Step 1: Generating image from text...")
        image_result = await runway_service.generate_image(
            prompt_text=request.prompt_text,
            ratio=request.image_ratio,
            seed=request.seed
        )
        
        if not image_result["success"]:
            return RunwayWorkflowResponse(
                success=False,
                image_result=ImageGenerationResponse(**image_result),
                error="Image generation failed",
                details=image_result.get("details")
            )
        
        # Step 2: Convert image to video
        logger.info("Step 2: Converting image to video...")
        video_result = await runway_service.image_to_video(
            prompt_image=image_result["image_url"],
            prompt_text=request.video_prompt,
            ratio=request.video_ratio,
            duration=request.video_duration
        )
        
        if not video_result["success"]:
            return RunwayWorkflowResponse(
                success=False,
                image_result=ImageGenerationResponse(**image_result),
                video_result=VideoGenerationResponse(**video_result),
                error="Video generation failed",
                details=video_result.get("details")
            )
        
        # Both steps successful
        return RunwayWorkflowResponse(
            success=True,
            image_result=ImageGenerationResponse(**image_result),
            video_result=VideoGenerationResponse(**video_result)
        )
        
    except Exception as e:
        logger.error(f"Unexpected error in workflow endpoint: {str(e)}")
        return RunwayWorkflowResponse(
            success=False,
            error="Workflow failed",
            details=str(e)
        )

@router.get("/status")
async def get_runway_status() -> Dict[str, Any]:
    """
    Get Runway ML service status and configuration.
    """
    try:
        api_key_configured = bool(settings.RUNWAY_API_KEY)
        
        return {
            "service": "Runway ML",
            "status": "online" if api_key_configured else "configuration_required",
            "api_key_configured": api_key_configured,
            "models": {
                "image_generation": "gen4_image",
                "video_generation": "gen4_turbo"
            },
            "supported_ratios": {
                "image": [
                    "1920:1080", "1080:1920", "1024:1024", "1360:768", "1080:1080",
                    "1168:880", "1440:1080", "1080:1440", "1808:768", "2112:912",
                    "1280:720", "720:1280", "720:720", "960:720", "720:960", "1680:720"
                ],
                "video": [
                    "1280:720", "720:1280", "1104:832", "832:1104", "960:960", "1584:672"
                ]
            },
            "video_durations": [5, 10]
        }
        
    except Exception as e:
        logger.error(f"Error getting Runway status: {str(e)}")
        return {
            "service": "Runway ML",
            "status": "error",
            "error": str(e)
        }

@router.post("/download-media")
async def download_media(
    request: MediaDownloadRequest,
    _: None = Depends(verify_runway_api_key)
) -> StreamingResponse:
    """
    Download media files (images/videos) from Runway ML URLs.

    This endpoint acts as a proxy to download media files from Runway ML
    and serve them to the client, avoiding CORS issues.
    """
    try:
        logger.info(f"Downloading media from URL: {request.url}")

        async with httpx.AsyncClient() as client:
            response = await client.get(request.url)
            response.raise_for_status()

            # Get content type from the response
            content_type = response.headers.get("content-type", "application/octet-stream")

            # Create a streaming response
            def generate():
                yield response.content

            return StreamingResponse(
                io.BytesIO(response.content),
                media_type=content_type,
                headers={
                    "Content-Disposition": f"attachment; filename={request.filename}",
                    "Content-Length": str(len(response.content))
                }
            )

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error downloading media: {e}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Failed to download media: {e.response.text}"
        )
    except Exception as e:
        logger.error(f"Unexpected error downloading media: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )
