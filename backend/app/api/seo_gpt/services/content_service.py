"""
Content Service
Business logic for content management operations
"""

import logging
import uuid
from datetime import datetime
from typing import Optional, List
from sqlalchemy.orm import Session
from fastapi import HTTPException

from app.db.seo_gpt_models import (
    SEOGPTProject, 
    ProjectContent
)
from ..schemas.content import (
    ProjectContentCreateRequest,
    ProjectContentUpdateRequest,
    ProjectContentResponse,
    ContentListResponse
)

logger = logging.getLogger(__name__)


class ContentService:
    """Service class for content management operations"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_project_contents(
        self,
        project_id: str,
        content_type: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> ContentListResponse:
        """Get all content items for a specific project"""
        try:
            # Verify project exists
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Build query
            query = self.db.query(ProjectContent).filter(ProjectContent.project_id == project_id)
            
            # Apply filters
            if content_type:
                query = query.filter(ProjectContent.content_type == content_type)
            if status:
                query = query.filter(ProjectContent.status == status)
            
            # Get contents with pagination
            contents = query.order_by(ProjectContent.updated_at.desc()).offset(offset).limit(limit).all()
            
            content_list = [self._content_to_response(content) for content in contents]
            
            return ContentListResponse(
                status="success",
                contents=content_list,
                total=len(content_list),
                limit=limit,
                offset=offset
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to get project contents: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to get project contents: {str(e)}")
    
    def create_project_content(
        self,
        project_id: str,
        request: ProjectContentCreateRequest
    ) -> ProjectContentResponse:
        """Create a new content item within a project"""
        try:
            # Verify project exists
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Generate content ID
            content_id = str(uuid.uuid4())
            
            # Calculate content metrics
            content_text = request.content_text or ""
            word_count = len(content_text.strip().split()) if content_text.strip() else 0
            content_length = len(content_text)
            
            # Generate slug if not provided
            slug = request.slug
            if not slug and request.title:
                slug = request.title.lower().replace(" ", "-").replace("_", "-")
                # Remove special characters
                import re
                slug = re.sub(r'[^a-z0-9\-]', '', slug)
            
            # Create new content
            new_content = ProjectContent(
                content_id=content_id,
                project_id=project_id,
                title=request.title,
                content_type=request.content_type,
                content_text=content_text,
                content_html=request.content_html,
                word_count=word_count,
                content_length=content_length,
                meta_description=request.meta_description,
                slug=slug,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            # Set target keywords
            if request.target_keywords:
                new_content.target_keywords_list = request.target_keywords
            
            self.db.add(new_content)
            self.db.commit()
            self.db.refresh(new_content)
            
            logger.info(f"✅ Created new content: {content_id} in project: {project_id}")
            
            return self._content_to_response(new_content)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Error creating content in project {project_id}: {str(e)}")
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Error creating content: {str(e)}")
    
    def get_project_content(
        self,
        project_id: str,
        content_id: str
    ) -> ProjectContentResponse:
        """Get a specific content item from a project"""
        try:
            # Verify project exists
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Get content
            content = self.db.query(ProjectContent).filter(
                ProjectContent.content_id == content_id,
                ProjectContent.project_id == project_id
            ).first()
            
            if not content:
                raise HTTPException(status_code=404, detail="Content not found")
            
            return self._content_to_response(content)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to get content: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to get content: {str(e)}")
    
    def update_project_content(
        self,
        project_id: str,
        content_id: str,
        request: ProjectContentUpdateRequest
    ) -> ProjectContentResponse:
        """Update a specific content item in a project"""
        try:
            logger.info(f"🔄 PATCH REQUEST - Starting update process")
            logger.info(f"🔄 PATCH REQUEST - project_id: {project_id}")
            logger.info(f"🔄 PATCH REQUEST - content_id: {content_id}")
            logger.info(f"🔄 PATCH REQUEST - request data: {request.model_dump()}")

            # Verify project exists
            logger.info(f"🔍 DATABASE - Verifying project exists...")
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == project_id
            ).first()

            if not project:
                logger.error(f"❌ PROJECT NOT FOUND - project_id: {project_id}")
                raise HTTPException(status_code=404, detail="Project not found")

            logger.info(f"✅ PROJECT FOUND - project_id: {project.project_id}, title: {project.title}")

            # Get content
            logger.info(f"🔍 DATABASE - Searching for content...")
            content = self.db.query(ProjectContent).filter(
                ProjectContent.project_id == project_id,
                ProjectContent.content_id == content_id
            ).first()

            if not content:
                logger.error(f"❌ CONTENT NOT FOUND - content_id: {content_id}, project_id: {project_id}")
                raise HTTPException(status_code=404, detail="Content not found")

            logger.info(f"✅ CONTENT FOUND - title: {content.title}")
            logger.info(f"✅ CONTENT FOUND - current content_text length: {len(content.content_text or '')}")
            logger.info(f"✅ CONTENT FOUND - current content_html length: {len(content.content_html or '')}")
            
            # Update fields - exact match to original logic
            if request.title is not None:
                content.title = request.title
            if request.content_text is not None:
                content.content_text = request.content_text
                content.word_count = len(request.content_text.strip().split()) if request.content_text.strip() else 0
                content.content_length = len(request.content_text)
            if request.content_html is not None:
                content.content_html = request.content_html
            if request.status is not None:
                content.status = request.status
                if request.status == "published" and not content.published_at:
                    content.published_at = datetime.utcnow()
            if request.target_keywords is not None:
                content.target_keywords_list = request.target_keywords
            if request.meta_description is not None:
                content.meta_description = request.meta_description
            if request.slug is not None:
                content.slug = request.slug
            if request.current_gpt_rank_score is not None:
                content.current_gpt_rank_score = request.current_gpt_rank_score
            if request.seo_score is not None:
                content.seo_score = request.seo_score
            
            content.updated_at = datetime.utcnow()

            # Log before commit
            logger.info(f"🔄 DATABASE - Before commit:")
            logger.info(f"   - title: {content.title}")
            logger.info(f"   - content_text length: {len(content.content_text or '')}")
            logger.info(f"   - content_html length: {len(content.content_html or '')}")
            logger.info(f"   - word_count: {content.word_count}")

            logger.info(f"🔄 DATABASE - Committing changes...")
            self.db.commit()

            logger.info(f"🔄 DATABASE - Refreshing content...")
            self.db.refresh(content)

            # Log after commit
            logger.info(f"✅ DATABASE - After commit:")
            logger.info(f"   - title: {content.title}")
            logger.info(f"   - content_text length: {len(content.content_text or '')}")
            logger.info(f"   - content_html length: {len(content.content_html or '')}")
            
            logger.info(f"✅ Updated content: {content_id} in project: {project_id}")
            
            return self._content_to_response(content)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to update content: {str(e)}")
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update content: {str(e)}")
    
    def delete_project_content(
        self,
        project_id: str,
        content_id: str
    ) -> dict:
        """Delete a content item from a project"""
        try:
            # Verify project exists
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Find and delete content
            content = self.db.query(ProjectContent).filter(
                ProjectContent.project_id == project_id,
                ProjectContent.content_id == content_id
            ).first()
            
            if not content:
                raise HTTPException(status_code=404, detail="Content not found")
            
            self.db.delete(content)
            self.db.commit()
            
            logger.info(f"✅ Deleted content: {content_id} from project: {project_id}")
            
            return {"status": "success", "message": "Content deleted successfully"}
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to delete content: {str(e)}")
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to delete content: {str(e)}")
    
    def _content_to_response(self, content: ProjectContent) -> ProjectContentResponse:
        """Convert content model to response schema"""
        return ProjectContentResponse(
            content_id=content.content_id,
            project_id=content.project_id,
            title=content.title,
            content_type=content.content_type,
            content_text=content.content_text,
            content_html=content.content_html,
            status=content.status,
            target_keywords=content.target_keywords_list,
            meta_description=content.meta_description,
            slug=content.slug,
            current_gpt_rank_score=content.current_gpt_rank_score or 0.0,
            seo_score=content.seo_score or 0.0,
            created_at=content.created_at.isoformat(),
            updated_at=content.updated_at.isoformat()
        )
