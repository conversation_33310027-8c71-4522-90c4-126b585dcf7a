"""
Project Service
Business logic for project CRUD operations
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import func
from fastapi import HTTPException

from app.db.seo_gpt_models import (
    SEOGPTProject, 
    ContentAnalysis, 
    GPTRankHistory, 
    KeywordResearch, 
    ProjectContent,
    ProjectStatus,
    ContentType
)
from ..schemas.project import (
    ProjectCreateRequest,
    ProjectUpdateRequest,
    ProjectResponse,
    ProjectListResponse,
    DashboardStatsResponse
)

logger = logging.getLogger(__name__)


class ProjectService:
    """Service class for project management operations"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_project(self, request: ProjectCreateRequest) -> ProjectResponse:
        """Create a new SEO GPT Optimizer project"""
        try:
            project_id = str(uuid.uuid4())
            
            # Create new project
            new_project = SEOGPTProject(
                project_id=project_id,
                title=request.title,
                topic=request.topic or request.title,
                target_language=request.target_language,
                content_type=request.content_type,
                target_gpt_rank_score=request.target_gpt_rank_score,
                status=ProjectStatus.DRAFT,
                user_id=request.user_id,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.db.add(new_project)
            self.db.commit()
            self.db.refresh(new_project)
            
            logger.info(f"✅ Created new SEO GPT project: {project_id}")
            
            return self._project_to_response(new_project)
            
        except Exception as e:
            logger.error(f"❌ Project creation failed: {str(e)}")
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Project creation failed: {str(e)}")
    
    def get_project(self, project_id: str) -> ProjectResponse:
        """Get a specific project by ID"""
        try:
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            return self._project_to_response(project)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to get project: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to get project: {str(e)}")
    
    def update_project(self, project_id: str, updates: ProjectUpdateRequest) -> ProjectResponse:
        """Update a specific project"""
        try:
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Update allowed fields
            update_data = updates.dict(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(project, field):
                    setattr(project, field, value)
            
            project.updated_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(project)
            
            logger.info(f"✅ Updated project: {project_id}")
            
            return self._project_to_response(project)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to update project: {str(e)}")
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update project: {str(e)}")
    
    def delete_project(self, project_id: str) -> dict:
        """Delete a specific project and all related data"""
        try:
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Delete related records first to avoid foreign key constraints
            project_internal_id = project.id
            
            # Delete project contents
            self.db.query(ProjectContent).filter(
                ProjectContent.project_id == project_internal_id
            ).delete(synchronize_session=False)
            
            # Delete content analyses
            self.db.query(ContentAnalysis).filter(
                ContentAnalysis.project_id == project_internal_id
            ).delete(synchronize_session=False)
            
            # Delete GPT rank history
            self.db.query(GPTRankHistory).filter(
                GPTRankHistory.project_id == project_internal_id
            ).delete(synchronize_session=False)
            
            # Delete keyword research
            self.db.query(KeywordResearch).filter(
                KeywordResearch.project_id == project_internal_id
            ).delete(synchronize_session=False)
            
            # Delete the project
            self.db.delete(project)
            self.db.commit()
            
            logger.info(f"✅ Successfully deleted project: {project_id}")
            
            return {"status": "success", "message": "Project deleted successfully"}
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to delete project: {str(e)}")
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to delete project: {str(e)}")
    
    def list_projects(
        self, 
        user_id: Optional[str] = None,
        status: Optional[ProjectStatus] = None,
        limit: int = 20,
        offset: int = 0
    ) -> ProjectListResponse:
        """List projects with optional filtering"""
        try:
            query = self.db.query(SEOGPTProject)
            
            if user_id:
                query = query.filter(SEOGPTProject.user_id == user_id)
            
            if status:
                query = query.filter(SEOGPTProject.status == status)
            
            projects = query.order_by(SEOGPTProject.updated_at.desc()).offset(offset).limit(limit).all()
            
            project_list = [self._project_to_response(project) for project in projects]
            
            return ProjectListResponse(
                status="success",
                projects=project_list,
                total=len(project_list),
                limit=limit,
                offset=offset
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to list projects: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to list projects: {str(e)}")
    
    def get_dashboard_stats(self, user_id: Optional[str] = None) -> DashboardStatsResponse:
        """Get dashboard analytics statistics"""
        try:
            # Base query
            query = self.db.query(SEOGPTProject)
            if user_id:
                query = query.filter(SEOGPTProject.user_id == user_id)
            
            # Total projects
            total_projects = query.count()
            
            # Active projects
            active_projects = query.filter(
                SEOGPTProject.status.in_([
                    ProjectStatus.RESEARCHING, 
                    ProjectStatus.WRITING, 
                    ProjectStatus.OPTIMIZING
                ])
            ).count()
            
            # Average scores
            avg_current_score = self.db.query(func.avg(SEOGPTProject.current_gpt_rank_score)).filter(
                SEOGPTProject.current_gpt_rank_score > 0
            ).scalar() or 0.0
            
            # Recent activity (last 30 days)
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            recent_projects = query.filter(SEOGPTProject.created_at >= thirty_days_ago).count()
            
            # Total research conducted
            total_research = self.db.query(KeywordResearch).count()
            
            # Score improvements this month
            score_improvements = self.db.query(GPTRankHistory).filter(
                GPTRankHistory.created_at >= thirty_days_ago,
                GPTRankHistory.score_change > 0
            ).count()
            
            return DashboardStatsResponse(
                status="success",
                data={
                    "total_projects": total_projects,
                    "active_projects": active_projects,
                    "avg_gpt_rank_score": round(avg_current_score, 2),
                    "total_research_conducted": total_research,
                    "improvement_this_month": score_improvements
                }
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to get dashboard stats: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to get dashboard stats: {str(e)}")

    def get_score_trends(self, user_id: Optional[str] = None, days: int = 30) -> dict:
        """Get score trends over time"""
        try:
            # Calculate date range
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)

            # Get score history
            query = self.db.query(GPTRankHistory).filter(
                GPTRankHistory.created_at >= start_date,
                GPTRankHistory.created_at <= end_date
            )

            if user_id:
                # Join with projects to filter by user
                query = query.join(SEOGPTProject).filter(SEOGPTProject.user_id == user_id)

            score_history = query.order_by(GPTRankHistory.created_at.asc()).all()

            # Group by date
            daily_scores = {}
            for history in score_history:
                date_key = history.created_at.date().isoformat()
                if date_key not in daily_scores:
                    daily_scores[date_key] = {
                        "date": date_key,
                        "scores": [],
                        "avg_score": 0.0,
                        "improvements": 0,
                        "total_analyses": 0
                    }

                daily_scores[date_key]["scores"].append(history.gpt_rank_score)
                daily_scores[date_key]["total_analyses"] += 1
                if history.score_change > 0:
                    daily_scores[date_key]["improvements"] += 1

            # Calculate averages
            trends = []
            for date_data in daily_scores.values():
                date_data["avg_score"] = round(sum(date_data["scores"]) / len(date_data["scores"]), 2)
                del date_data["scores"]  # Remove raw scores to reduce response size
                trends.append(date_data)

            return {
                "status": "success",
                "score_trends": sorted(trends, key=lambda x: x["date"]),
                "period_days": days,
                "total_data_points": len(trends)
            }

        except Exception as e:
            logger.error(f"❌ Failed to get score trends: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to get score trends: {str(e)}")

    def search_projects(
        self,
        query: str,
        status: Optional[str] = None,
        content_type: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> dict:
        """Search projects by query string"""
        try:
            # Build search query
            search_query = self.db.query(SEOGPTProject)

            # Apply text search on title and topic
            if query:
                search_query = search_query.filter(
                    (SEOGPTProject.title.ilike(f"%{query}%")) |
                    (SEOGPTProject.topic.ilike(f"%{query}%")) |
                    (SEOGPTProject.content_text.ilike(f"%{query}%"))
                )

            # Apply filters
            if status:
                status_list = status.split(',')
                search_query = search_query.filter(SEOGPTProject.status.in_(status_list))

            if content_type:
                content_type_list = content_type.split(',')
                search_query = search_query.filter(SEOGPTProject.content_type.in_(content_type_list))

            # Get total count
            total = search_query.count()

            # Get paginated results
            projects = search_query.order_by(
                SEOGPTProject.updated_at.desc()
            ).offset(offset).limit(limit).all()

            project_list = [self._project_to_response(project) for project in projects]

            return {
                "status": "success",
                "projects": project_list,
                "total": total,
                "limit": limit,
                "offset": offset,
                "query": query
            }

        except Exception as e:
            logger.error(f"❌ Failed to search projects: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to search projects: {str(e)}")

    def _project_to_response(self, project: SEOGPTProject) -> ProjectResponse:
        """Convert project model to response schema"""
        return ProjectResponse(
            project_id=project.project_id,
            title=project.title,
            topic=project.topic,
            status=project.status.value,
            content_type=project.content_type.value,
            target_language=project.target_language,
            current_gpt_rank_score=project.current_gpt_rank_score,
            target_gpt_rank_score=project.target_gpt_rank_score,
            best_gpt_rank_score=project.best_gpt_rank_score,
            content_text=project.content_text,
            content_length=project.content_length or 0,
            word_count=project.word_count or 0,
            created_at=project.created_at.isoformat(),
            updated_at=project.updated_at.isoformat()
        )
