"""
Research Service
Business logic for SEO research operations
"""

import logging
import uuid
from datetime import datetime
from typing import Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException

from app.db.seo_gpt_models import (
    SEOGPTProject, 
    KeywordResearch
)
from app.services.seo_gpt_research import SEOGPTResearchService
from ..schemas.research import (
    ResearchRequest,
    ResearchResponse
)

logger = logging.getLogger(__name__)


class ResearchService:
    """Service class for research operations"""
    
    def __init__(self, db: Session):
        self.db = db
        self.research_engine = SEOGPTResearchService()
    
    async def conduct_research(self, request: ResearchRequest) -> ResearchResponse:
        """Conduct comprehensive research for a topic"""
        try:
            logger.info(f"🔍 Starting research for topic: '{request.topic}'")
            
            # Conduct research using the research engine
            research_results = await self.research_engine.conduct_comprehensive_research(
                topic=request.topic,
                target_language=request.target_language,
                include_reddit=request.include_reddit,
                include_quora=request.include_quora,
                target_country=request.target_country or "ES",
                include_news=request.include_news or False
            )
            
            if research_results.get("status") == "error":
                raise HTTPException(status_code=500, detail=research_results.get("error_message"))
            
            # Store research results in database
            research_id = str(uuid.uuid4())
            
            # Get project if project_id is provided
            project_db_id = None
            if request.project_id:
                project = self.db.query(SEOGPTProject).filter(
                    SEOGPTProject.project_id == request.project_id
                ).first()
                if project:
                    project_db_id = project.id
                    logger.info(f"🔗 Linking research to project: {request.project_id}")
            
            keyword_research = KeywordResearch(
                research_id=research_id,
                project_id=project_db_id,  # Link to project if provided
                topic=request.topic,
                target_language=request.target_language,
                research_type="comprehensive",
                intent_analysis=research_results.get("intent_analysis"),
                google_results=research_results.get("google_results"),
                social_insights=research_results.get("social_insights"),
                gpt_reference=research_results.get("gpt_reference"),
                entities_and_questions=research_results.get("entities_and_questions"),
                content_opportunities=research_results.get("content_opportunities"),
                research_confidence=research_results.get("research_summary", {}).get("research_confidence", 0.0),
                processing_time=research_results.get("processing_time", 0.0),
                total_sources_analyzed=len(research_results.get("google_results", {}).get("results", [])),
                status="completed"
            )
            
            self.db.add(keyword_research)
            self.db.commit()
            self.db.refresh(keyword_research)
            
            logger.info(f"✅ Research completed and stored for topic: '{request.topic}'")
            
            return ResearchResponse(
                status="success",
                research_id=research_id,
                topic=research_results["topic"],
                target_language=research_results["target_language"],
                processing_time=research_results["processing_time"],
                timestamp=research_results["timestamp"],
                intent_analysis=research_results["intent_analysis"],
                google_results=research_results["google_results"],
                social_insights=research_results["social_insights"],
                gpt_reference=research_results["gpt_reference"],
                entities_and_questions=research_results["entities_and_questions"],
                content_opportunities=research_results["content_opportunities"],
                research_summary=research_results["research_summary"],
                research_quality_metrics=research_results["research_quality_metrics"],
                message="Research completed successfully"
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Research failed for topic '{request.topic}': {str(e)}")
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Research failed: {str(e)}")
    
    def get_project_research(
        self,
        project_id: str,
        limit: int = 20,
        offset: int = 0
    ) -> dict:
        """Get research data for a specific project"""
        try:
            # Verify project exists
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Get research data
            research_query = self.db.query(KeywordResearch).filter(
                KeywordResearch.project_id == project.id
            )
            
            research_items = research_query.order_by(
                KeywordResearch.created_at.desc()
            ).offset(offset).limit(limit).all()
            
            research_list = []
            for research in research_items:
                research_list.append({
                    "research_id": research.research_id,
                    "topic": research.topic,
                    "target_language": research.target_language,
                    "research_type": research.research_type,
                    "research_confidence": research.research_confidence,
                    "processing_time": research.processing_time,
                    "total_sources_analyzed": research.total_sources_analyzed,
                    "status": research.status,
                    "created_at": research.created_at.isoformat(),
                    "intent_analysis": research.intent_analysis,
                    "google_results": research.google_results,
                    "social_insights": research.social_insights,
                    "gpt_reference": research.gpt_reference,
                    "entities_and_questions": research.entities_and_questions,
                    "content_opportunities": research.content_opportunities
                })
            
            return {
                "status": "success",
                "research": research_list,
                "total": len(research_list),
                "limit": limit,
                "offset": offset
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to get project research: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to get project research: {str(e)}")
