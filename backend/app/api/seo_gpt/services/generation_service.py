"""
Generation Service
Business logic for content generation operations
"""

import logging
from datetime import datetime
from typing import List
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Exception

from app.db.seo_gpt_models import SEOGPTProject
from app.services.ai_content_generator import RealAIContentGenerator, ContentGenerationRequest as AIContentRequest
from app.services.saio_agent_service import SAIOAgentService

from app.core.config import settings
from ..schemas.generation import (
    BlogGenerationRequest,
    SAIOContentRequest,
    ContentGenerationResponse,
    SAIOContentResponse
)

logger = logging.getLogger(__name__)


class GenerationService:
    """Service class for content generation operations"""
    
    def __init__(self, db: Session):
        self.db = db
        self.ai_content_generator = RealAIContentGenerator(
            openai_api_key=settings.OPENAI_API_KEY,
            gemini_api_key=settings.GEMINI_API_KEY
        )
        self.saio_agent_service = SAIOAgentService()

    
    async def generate_blog_content(self, request: BlogGenerationRequest) -> ContentGenerationResponse:
        """Generate complete blog content using AI content generation"""
        try:
            logger.info(f"🎨 Generating blog content for project: {request.project_id}")
            
            # Get project
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == request.project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            logger.info(f"🧠 Using AI content generator to create blog content...")
            
            # Create content generation request
            ai_request = AIContentRequest(
                topic=request.topic,
                keywords=[request.topic],  # Use topic as primary keyword
                content_type="blog",
                tone="professional",
                length=request.target_length,
                target_audience="general audience",
                include_images=False,
                seo_optimized=True,
                saio_optimized=True
            )
            
            # Generate blog content using AI content generator
            generated_content = await self.ai_content_generator.generate_content(ai_request)
            
            logger.info(f"✅ Blog content generated! Word count: {generated_content.word_count}")
            
            # Use the generated content
            blog_content = generated_content.content
            
            # Images generation removed - focusing on text content only
            
            # Update project with generated content
            project.content_text = blog_content
            project.content_length = len(blog_content)
            project.word_count = len(blog_content.split())
            project.updated_at = datetime.utcnow()
            
            self.db.commit()
            
            logger.info(f"✅ Blog content generated successfully for project: {request.project_id}")
            
            return ContentGenerationResponse(
                status="success",
                project_id=request.project_id,
                message="Blog content generated successfully",
                data={
                    "content": blog_content,
                    "title": generated_content.title,
                    "meta_description": generated_content.meta_description,

                    "seo_analysis": {
                        "seo_score": generated_content.seo_score,
                        "saio_score": generated_content.saio_score,
                        "keywords": generated_content.keywords
                    },
                    "content_stats": {
                        "word_count": generated_content.word_count,
                        "character_count": len(blog_content),
                        "estimated_reading_time": f"{max(1, generated_content.word_count // 200)} min"
                    }
                }
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Blog generation failed: {str(e)}")
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Blog generation failed: {str(e)}")
    
    async def generate_saio_content(self, request: SAIOContentRequest) -> SAIOContentResponse:
        """Generate SAIO-optimized content"""
        try:
            logger.info(f"🚀 Generating SAIO content for project: {request.project_id}")
            
            # Get project
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == request.project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Generate SAIO-optimized content
            saio_content = await self.saio_agent_service.generate_saio_optimized_content(
                topic=request.topic,
                content_type=request.content_type
            )
            
            if "error" in saio_content:
                raise HTTPException(status_code=500, detail=saio_content["error"])
            
            # Update project with generated content
            project.content_text = saio_content["content"]
            project.content_length = len(saio_content["content"])
            project.word_count = len(saio_content["content"].split())
            project.updated_at = datetime.utcnow()
            
            self.db.commit()
            
            logger.info(f"✅ SAIO content generated - Score: {saio_content['saio_analysis']['saio_score']}")
            
            return SAIOContentResponse(
                status="success",
                project_id=request.project_id,
                message="SAIO content generated successfully",
                data={
                    "content": saio_content["content"],
                    "saio_analysis": saio_content["saio_analysis"],
                    "template_used": saio_content["template_used"],
                    "optimization_targets": request.optimize_for
                },
                world_first=True,
                research_based=True
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ SAIO content generation failed: {str(e)}")
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"SAIO content generation failed: {str(e)}")
    

