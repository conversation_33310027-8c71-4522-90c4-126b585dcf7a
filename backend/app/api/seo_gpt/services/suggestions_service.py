"""
Suggestions Service
Business logic for content suggestions and recommendations
"""

import logging
import re
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func
from fastapi import HTTPException

from app.db.seo_gpt_models import SEOGPTProject, KeywordResearch, ContentAnalysis
from app.services.seo_gpt_research import SEOGPTResearchService

logger = logging.getLogger(__name__)


class SuggestionsService:
    """Service class for content suggestions and recommendations"""
    
    def __init__(self, db: Session):
        self.db = db
        self.research_service = SEOGPTResearchService()
    
    async def get_topic_suggestions(self, partial: str, limit: int = 10) -> List[str]:
        """Get topic suggestions based on partial input"""
        try:
            suggestions = []
            
            # Get suggestions from existing projects
            project_topics = self.db.query(SEOGPTProject.topic).filter(
                SEOGPTProject.topic.ilike(f"%{partial}%")
            ).distinct().limit(limit // 2).all()
            
            suggestions.extend([topic[0] for topic in project_topics if topic[0]])
            
            # Get suggestions from research data
            research_topics = self.db.query(KeywordResearch.topic).filter(
                KeywordResearch.topic.ilike(f"%{partial}%")
            ).distinct().limit(limit // 2).all()
            
            suggestions.extend([topic[0] for topic in research_topics if topic[0]])
            
            # Add some predefined suggestions based on common patterns
            predefined_suggestions = self._get_predefined_topic_suggestions(partial)
            suggestions.extend(predefined_suggestions)
            
            # Remove duplicates and limit
            unique_suggestions = list(dict.fromkeys(suggestions))[:limit]
            
            # If we don't have enough suggestions, add some AI-generated ones
            if len(unique_suggestions) < limit:
                ai_suggestions = await self._get_ai_topic_suggestions(partial, limit - len(unique_suggestions))
                unique_suggestions.extend(ai_suggestions)
            
            return unique_suggestions[:limit]
            
        except Exception as e:
            logger.error(f"❌ Failed to get topic suggestions: {str(e)}")
            # Return fallback suggestions
            return self._get_fallback_topic_suggestions(partial)
    
    async def get_keyword_suggestions(self, topic: str, limit: int = 20, language: str = "es") -> List[str]:
        """Get keyword suggestions for a specific topic"""
        try:
            keywords = []
            
            # Get keywords from existing research
            research_data = self.db.query(KeywordResearch).filter(
                KeywordResearch.topic.ilike(f"%{topic}%")
            ).first()
            
            if research_data and research_data.entities_and_questions:
                entities = research_data.entities_and_questions.get("entities", [])
                keywords.extend(entities[:limit // 2])
            
            # Generate semantic keywords
            semantic_keywords = self._generate_semantic_keywords(topic, language)
            keywords.extend(semantic_keywords)
            
            # Add long-tail variations
            long_tail_keywords = self._generate_long_tail_keywords(topic, language)
            keywords.extend(long_tail_keywords)
            
            # Remove duplicates and limit
            unique_keywords = list(dict.fromkeys(keywords))[:limit]
            
            return unique_keywords
            
        except Exception as e:
            logger.error(f"❌ Failed to get keyword suggestions: {str(e)}")
            return self._get_fallback_keyword_suggestions(topic, language)
    
    async def get_content_optimization_suggestions(
        self, 
        content: str, 
        target_keywords: List[str], 
        content_type: str = "blog"
    ) -> List[Dict[str, Any]]:
        """Get content optimization suggestions"""
        try:
            suggestions = []
            
            # Analyze content structure
            word_count = len(content.split())
            paragraph_count = len([p for p in content.split('\n\n') if p.strip()])
            headings = self._extract_headings(content)
            
            # Word count suggestions
            if word_count < 300:
                suggestions.append({
                    "type": "content_length",
                    "priority": "high",
                    "suggestion": "Aumenta la longitud del contenido",
                    "details": f"El contenido tiene {word_count} palabras. Se recomienda al menos 300 palabras para mejor SEO.",
                    "action": "Añade más información relevante y ejemplos"
                })
            elif word_count > 2000:
                suggestions.append({
                    "type": "content_length",
                    "priority": "medium",
                    "suggestion": "Considera dividir el contenido",
                    "details": f"El contenido tiene {word_count} palabras. Contenido muy largo puede afectar la legibilidad.",
                    "action": "Divide en secciones o artículos separados"
                })
            
            # Heading structure suggestions
            if len(headings) < 2:
                suggestions.append({
                    "type": "structure",
                    "priority": "high",
                    "suggestion": "Añade más subtítulos",
                    "details": "Los subtítulos mejoran la estructura y SEO del contenido.",
                    "action": "Usa H2 y H3 para organizar el contenido"
                })
            
            # Keyword density suggestions
            for keyword in target_keywords:
                density = self._calculate_keyword_density(content, keyword)
                if density < 0.5:
                    suggestions.append({
                        "type": "keyword_density",
                        "priority": "medium",
                        "suggestion": f"Aumenta la densidad de '{keyword}'",
                        "details": f"La palabra clave '{keyword}' aparece {density:.1f}% del contenido.",
                        "action": f"Incluye '{keyword}' de forma natural en el texto"
                    })
                elif density > 3.0:
                    suggestions.append({
                        "type": "keyword_density",
                        "priority": "medium",
                        "suggestion": f"Reduce la densidad de '{keyword}'",
                        "details": f"La palabra clave '{keyword}' aparece {density:.1f}% del contenido (muy alto).",
                        "action": f"Usa sinónimos y variaciones de '{keyword}'"
                    })
            
            # SAIO optimization suggestions
            if '?' not in content:
                suggestions.append({
                    "type": "saio",
                    "priority": "high",
                    "suggestion": "Añade preguntas frecuentes",
                    "details": "Las preguntas mejoran el ranking en IA conversacional.",
                    "action": "Incluye una sección de FAQ o preguntas en el texto"
                })
            
            if not any(marker in content for marker in ['•', '-', '1.', '2.']):
                suggestions.append({
                    "type": "saio",
                    "priority": "medium",
                    "suggestion": "Añade listas o puntos",
                    "details": "Las listas mejoran la legibilidad y el ranking en IA.",
                    "action": "Organiza información en listas numeradas o con viñetas"
                })
            
            return suggestions
            
        except Exception as e:
            logger.error(f"❌ Failed to get content optimization suggestions: {str(e)}")
            return []
    
    def _get_predefined_topic_suggestions(self, partial: str) -> List[str]:
        """Get predefined topic suggestions based on common patterns"""
        partial_lower = partial.lower()
        
        suggestions = []
        
        # Technology topics
        if any(word in partial_lower for word in ['tech', 'tecnología', 'digital', 'ai', 'ia']):
            suggestions.extend([
                "Inteligencia Artificial en el Marketing",
                "Transformación Digital para Empresas",
                "Tecnología Blockchain Explicada",
                "Automatización de Procesos Empresariales"
            ])
        
        # Marketing topics
        if any(word in partial_lower for word in ['marketing', 'seo', 'social', 'content']):
            suggestions.extend([
                "Estrategias de Marketing Digital 2024",
                "SEO para Principiantes",
                "Marketing en Redes Sociales",
                "Content Marketing Efectivo"
            ])
        
        # Business topics
        if any(word in partial_lower for word in ['negocio', 'empresa', 'business', 'startup']):
            suggestions.extend([
                "Cómo Crear una Startup Exitosa",
                "Estrategias de Crecimiento Empresarial",
                "Liderazgo en Tiempos de Crisis",
                "Innovación en Modelos de Negocio"
            ])
        
        return [s for s in suggestions if partial_lower in s.lower()]
    
    async def _get_ai_topic_suggestions(self, partial: str, limit: int) -> List[str]:
        """Get AI-generated topic suggestions"""
        # This would integrate with an AI service for topic generation
        # For now, return some contextual suggestions
        return [
            f"Guía Completa de {partial.title()}",
            f"Mejores Prácticas en {partial.title()}",
            f"Tendencias 2024 en {partial.title()}",
            f"Cómo Optimizar {partial.title()}"
        ][:limit]
    
    def _get_fallback_topic_suggestions(self, partial: str) -> List[str]:
        """Get fallback topic suggestions when other methods fail"""
        return [
            f"Introducción a {partial}",
            f"Beneficios de {partial}",
            f"Cómo implementar {partial}",
            f"Mejores herramientas para {partial}",
            f"Tendencias en {partial}"
        ]
    
    def _generate_semantic_keywords(self, topic: str, language: str) -> List[str]:
        """Generate semantic keywords related to the topic"""
        topic_lower = topic.lower()
        keywords = []
        
        # Add variations
        keywords.append(f"qué es {topic_lower}")
        keywords.append(f"cómo {topic_lower}")
        keywords.append(f"beneficios de {topic_lower}")
        keywords.append(f"mejores {topic_lower}")
        keywords.append(f"{topic_lower} para principiantes")
        keywords.append(f"guía de {topic_lower}")
        
        return keywords
    
    def _generate_long_tail_keywords(self, topic: str, language: str) -> List[str]:
        """Generate long-tail keyword variations"""
        topic_lower = topic.lower()
        
        return [
            f"cómo implementar {topic_lower} en mi empresa",
            f"mejores herramientas de {topic_lower} 2024",
            f"ventajas y desventajas de {topic_lower}",
            f"tutorial paso a paso de {topic_lower}",
            f"errores comunes en {topic_lower}",
            f"tendencias futuras en {topic_lower}"
        ]
    
    def _get_fallback_keyword_suggestions(self, topic: str, language: str) -> List[str]:
        """Get fallback keyword suggestions"""
        return self._generate_semantic_keywords(topic, language)
    
    def _extract_headings(self, content: str) -> List[Dict[str, Any]]:
        """Extract headings from content"""
        headings = []
        for match in re.finditer(r'^(#{1,6})\s+(.+)$', content, re.MULTILINE):
            level = len(match.group(1))
            text = match.group(2).strip()
            headings.append({"level": level, "text": text})
        return headings
    
    def _calculate_keyword_density(self, content: str, keyword: str) -> float:
        """Calculate keyword density as percentage"""
        content_lower = content.lower()
        keyword_lower = keyword.lower()
        
        word_count = len(content.split())
        keyword_count = content_lower.count(keyword_lower)
        
        if word_count == 0:
            return 0.0
        
        return (keyword_count / word_count) * 100
