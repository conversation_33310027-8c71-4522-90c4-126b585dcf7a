"""
Analysis Service
Business logic for content analysis operations
"""

import logging
import re
from typing import Optional, List
from sqlalchemy.orm import Session
from fastapi import HTTPException

from app.db.seo_gpt_models import (
    SEOGPTProject, 
    ContentAnalysis, 
    GPTRankHistory
)
from app.services.gpt_rank_engine import GPTRankEngineService
from app.services.contextual_seo_analyzer import ContextualSEOAnalyzer
from app.services.saio_agent_service import SAIOAgentService
from ..schemas.content import ContentAnalysisRequest
from ..schemas.analysis import (
    ContentAnalysisResponse,
    SAIOAnalysisResponse,
    ConfidenceAnalysisResponse,
    ScoreResponse
)

logger = logging.getLogger(__name__)


class AnalysisService:
    """Service class for content analysis operations"""
    
    def __init__(self, db: Session):
        self.db = db
        self.gpt_rank_service = GPTRankEngineService()
        self.contextual_analyzer = ContextualSEOAnalyzer()
        self.saio_agent_service = SAIOAgentService()
    
    async def analyze_content_seo(self, request: ContentAnalysisRequest) -> ContentAnalysisResponse:
        """Analyze content for SEO and SAIO optimization"""
        try:
            logger.info(f"🔍 Analyzing content for project: {request.project_id}")
            
            # Get project
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == request.project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Check if deep analysis is requested
            if request.deep_analysis:
                logger.info("🧠 Performing deep contextual analysis")
                contextual_results = await self.contextual_analyzer.analyze_content_deeply(
                    content=request.content,
                    target_keywords=request.target_keywords or []
                )
                
                return ContentAnalysisResponse(
                    status="success",
                    analysis={},
                    project_id=request.project_id,
                    analysis_type="contextual",
                    gpt_rank_score=contextual_results['scores']['overall_score'],
                    score_grade="Excelente" if contextual_results['scores']['overall_score'] > 80 else "Bueno" if contextual_results['scores']['overall_score'] > 60 else "Mejorable",
                    improvement_suggestions=contextual_results['suggestions'],
                    content_stats={
                        "word_count": contextual_results['content_analysis']['word_count'],
                        "paragraph_count": contextual_results['content_analysis']['paragraph_count'],
                        "sentence_count": contextual_results['content_analysis']['sentence_count']
                    },
                    component_scores={
                        "readability_score": contextual_results['scores']['readability_score'],
                        "structure_score": contextual_results['scores']['structure_score'],
                        "engagement_score": contextual_results['scores']['engagement_score'],
                        "clarity_score": contextual_results['scores']['readability_score']
                    },
                    weaknesses_identified=len(contextual_results['weaknesses']),
                    contextual_insights={
                        "main_topic": contextual_results['content_analysis'].get('word_frequency', {}),
                        "readability_level": contextual_results['content_analysis'].get('readability_estimate', 'fair'),
                        "structure_quality": "good" if contextual_results['content_analysis']['has_headings'] else "needs_improvement"
                    }
                )
            
            # Standard GPT Rank Engine analysis
            gpt_rank_results = await self.gpt_rank_service.analyze_content(
                content=request.content,
                target_keywords=request.target_keywords or [],
                content_type=request.content_type or "blog"
            )
            
            # Calculate additional metrics
            word_count = len(request.content.split())
            sentences = request.content.count('.') + request.content.count('!') + request.content.count('?')
            paragraphs = len([p for p in request.content.split('\n\n') if p.strip()])
            
            # Extract headings
            headings = []
            for match in re.finditer(r'^(#{1,6})\s+(.+)$', request.content, re.MULTILINE):
                level = len(match.group(1))
                text = match.group(2).strip()
                headings.append({"level": level, "text": text})
            
            # Calculate keyword density
            content_lower = request.content.lower()
            keyword_density = {}
            if request.target_keywords:
                for keyword in request.target_keywords:
                    count = content_lower.count(keyword.lower())
                    density = (count / word_count) * 100 if word_count > 0 else 0
                    keyword_density[keyword] = round(density, 2)
            
            # Calculate readability score (simplified)
            avg_sentence_length = word_count / max(sentences, 1)
            readability_score = max(0, min(100, 100 - (avg_sentence_length - 15) * 2))
            
            # Calculate SAIO score
            saio_score = 70  # Base score
            if '?' in request.content:
                saio_score += 5  # Has questions
            if any(marker in request.content for marker in ['•', '-', '1.', '2.']):
                saio_score += 5  # Has lists
            if len(headings) >= 3:
                saio_score += 5  # Good structure
            if word_count >= 800:
                saio_score += 5  # Sufficient length
            if any(word in content_lower for word in ['cómo', 'qué', 'por qué', 'cuándo']):
                saio_score += 5  # Question-focused content
            
            saio_score = min(100, saio_score)
            
            analysis_data = {
                "score": gpt_rank_results.get("overall_score", 75),
                "keywords": {
                    "primary": request.target_keywords[:3] if request.target_keywords else ["SEO", "contenido"],
                    "secondary": request.target_keywords[3:] if len(request.target_keywords or []) > 3 else ["marketing", "digital"],
                    "density": keyword_density
                },
                "readability": {
                    "score": int(readability_score),
                    "level": "Fácil" if readability_score > 80 else "Intermedio" if readability_score > 60 else "Difícil",
                    "suggestions": [
                        "Usa oraciones más cortas" if avg_sentence_length > 20 else "Longitud de oraciones adecuada",
                        "Añade más subtítulos" if len(headings) < 3 else "Estructura de títulos correcta"
                    ]
                },
                "structure": {
                    "headings": headings,
                    "paragraphs": paragraphs,
                    "wordCount": word_count,
                    "sentences": sentences
                },
                "saio": {
                    "score": saio_score,
                    "qAndA": '?' in request.content,
                    "lists": any(marker in request.content for marker in ['•', '-', '1.', '2.']),
                    "freshness": True,
                    "multimedia": 'img' in request.content.lower() or 'imagen' in request.content.lower(),
                    "sources": 'http' in request.content or 'fuente' in request.content.lower()
                },
                "suggestions": gpt_rank_results.get("suggestions", [
                    "Añade más palabras clave relevantes",
                    "Incluye preguntas frecuentes",
                    "Mejora la estructura con subtítulos",
                    "Añade elementos multimedia"
                ])
            }
            
            return ContentAnalysisResponse(
                status="success",
                analysis=analysis_data
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Content analysis failed: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Content analysis failed: {str(e)}")
    
    async def analyze_content_confidence(self, request: ContentAnalysisRequest) -> ConfidenceAnalysisResponse:
        """Analyze content confidence for LLM ranking"""
        try:
            logger.info(f"🧠 LLM confidence analysis requested for project: {request.project_id}")
            
            # Get project
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == request.project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Perform LLM confidence analysis
            confidence_results = self.contextual_analyzer.analyze_content_confidence(
                content=request.content,
                target_keywords=request.target_keywords or []
            )
            
            if confidence_results['status'] == 'error':
                raise HTTPException(status_code=500, detail="LLM confidence analysis failed")
            
            confidence_data = confidence_results['confidence_analysis']
            overall_confidence = confidence_data['overall_confidence']
            
            logger.info(f"✅ LLM confidence analysis completed - Score: {overall_confidence['overall_score']}")
            
            return ConfidenceAnalysisResponse(
                status="success",
                analysis_type="llm_ranking_confidence",
                project_id=request.project_id,
                overall_confidence={
                    "score": overall_confidence['overall_score'],
                    "badge": overall_confidence['confidence_badge'],
                    "color": overall_confidence['confidence_color'],
                    "quality_level": overall_confidence['quality_level'],
                    "llm_performance": "Evaluación para Perplexity, ChatGPT, Claude y otros LLMs",
                    "summary": {
                        "total_weaknesses": overall_confidence['weaknesses_count'],
                        "high_priority_issues": overall_confidence['high_priority_issues'],
                        "improvement_opportunities": overall_confidence['improvement_opportunities']
                    }
                },
                weaknesses_analysis=[
                    {
                        "type": weakness['type'],
                        "issue": weakness['issue'],
                        "severity": weakness['severity'],
                        "llm_impact": "Afecta ranking en sistemas de IA",
                        "confidence": {
                            "score": weakness['confidence_score'],
                            "badge": weakness['confidence_badge'],
                            "color": weakness['confidence_color']
                        },
                        "improvement_potential": weakness['improvement_potential'],
                        "current_value": weakness.get('current_value'),
                        "target_value": weakness.get('target_value')
                    }
                    for weakness in confidence_data['weaknesses_with_confidence']
                ],
                quality_metrics=overall_confidence.get('quality_metrics', {}),
                analysis_metadata={
                    "timestamp": confidence_data['analysis_timestamp'],
                    "trigger_type": confidence_data['trigger_type'],
                    "analysis_type": confidence_data['analysis_type'],
                    "target_llms": ["Perplexity", "ChatGPT", "Claude", "Gemini"],
                    "content_length": len(request.content)
                }
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ LLM confidence analysis failed: {str(e)}")
            raise HTTPException(status_code=500, detail=f"LLM confidence analysis failed: {str(e)}")
    
    async def analyze_saio_content(self, request: ContentAnalysisRequest) -> SAIOAnalysisResponse:
        """Analyze content with SAIO Agent"""
        try:
            logger.info(f"🧠 SAIO Agent analyzing content for project: {request.project_id}")
            
            # Get project
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == request.project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Analyze with SAIO Agent
            saio_results = await self.saio_agent_service.analyze_saio_score(
                content=request.content,
                title=project.title,
                url=""
            )
            
            if "error" in saio_results:
                raise HTTPException(status_code=500, detail=saio_results["error"])
            
            logger.info(f"✅ SAIO analysis completed - Score: {saio_results['saio_score']}")
            
            return SAIOAnalysisResponse(
                status="success",
                project_id=request.project_id,
                message="SAIO analysis completed",
                saio_results=saio_results,
                analysis_type="research_based_saio",
                world_first=True
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ SAIO analysis failed: {str(e)}")
            raise HTTPException(status_code=500, detail=f"SAIO analysis failed: {str(e)}")
    
    def get_project_score(self, project_id: str) -> ScoreResponse:
        """Get current GPT Rank Score for a project"""
        try:
            project = self.db.query(SEOGPTProject).filter(
                SEOGPTProject.project_id == project_id
            ).first()
            
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Get latest analysis
            latest_analysis = self.db.query(ContentAnalysis).filter(
                ContentAnalysis.project_id == project.id
            ).order_by(ContentAnalysis.created_at.desc()).first()
            
            # Get score history
            score_history = self.db.query(GPTRankHistory).filter(
                GPTRankHistory.project_id == project.id
            ).order_by(GPTRankHistory.created_at.desc()).limit(10).all()
            
            return ScoreResponse(
                status="success",
                project_id=project_id,
                current_score=project.current_gpt_rank_score,
                best_score=project.best_gpt_rank_score,
                target_score=project.target_gpt_rank_score,
                score_grade=latest_analysis.score_grade if latest_analysis else "N/A",
                confidence_level=latest_analysis.confidence_level if latest_analysis else "unknown",
                latest_analysis={
                    "analysis_id": latest_analysis.analysis_id,
                    "created_at": latest_analysis.created_at.isoformat(),
                    "component_scores": {
                        "semantic_similarity": latest_analysis.semantic_similarity_score,
                        "logical_coherence": latest_analysis.logical_coherence_score,
                        "authority_signals": latest_analysis.authority_signals_score,
                        "citability_score": latest_analysis.citability_score,
                        "clarity_score": latest_analysis.clarity_score,
                        "completeness_score": latest_analysis.completeness_score
                    }
                } if latest_analysis else None,
                score_history=[
                    {
                        "score": history.gpt_rank_score,
                        "change": history.score_change,
                        "grade": history.score_grade,
                        "created_at": history.created_at.isoformat()
                    }
                    for history in score_history
                ]
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to get project score: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to get project score: {str(e)}")

    async def batch_analyze_projects(self, project_ids: List[str]) -> List[dict]:
        """Batch analyze multiple projects"""
        try:
            results = []

            for project_id in project_ids:
                try:
                    # Get project
                    project = self.db.query(SEOGPTProject).filter(
                        SEOGPTProject.project_id == project_id
                    ).first()

                    if not project:
                        results.append({
                            "project_id": project_id,
                            "status": "error",
                            "error": "Project not found"
                        })
                        continue

                    # Create analysis request
                    request = ContentAnalysisRequest(
                        project_id=project_id,
                        content=project.content_text or "",
                        target_keywords=project.target_keywords_list or []
                    )

                    # Analyze content
                    analysis = await self.analyze_content_seo(request)

                    results.append({
                        "project_id": project_id,
                        "status": "success",
                        "analysis": analysis.dict()
                    })

                except Exception as e:
                    logger.error(f"❌ Failed to analyze project {project_id}: {str(e)}")
                    results.append({
                        "project_id": project_id,
                        "status": "error",
                        "error": str(e)
                    })

            return results

        except Exception as e:
            logger.error(f"❌ Failed to batch analyze projects: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to batch analyze projects: {str(e)}")

    async def analyze_content_structure(self, content: str) -> dict:
        """Analyze content structure"""
        try:
            import re

            # Extract headings
            headings = []
            for match in re.finditer(r'^(#{1,6})\s+(.+)$', content, re.MULTILINE):
                level = len(match.group(1))
                text = match.group(2).strip()
                position = match.start()
                headings.append({
                    "level": level,
                    "text": text,
                    "position": position
                })

            # Count paragraphs and sentences
            paragraphs = len([p for p in content.split('\n\n') if p.strip()])
            sentences = content.count('.') + content.count('!') + content.count('?')

            # Calculate structure score
            structure_score = self._calculate_structure_score(content, headings, paragraphs)

            # Generate recommendations
            recommendations = self._generate_structure_recommendations(content, headings, paragraphs)

            return {
                "status": "success",
                "headings": headings,
                "paragraphs": paragraphs,
                "sentences": sentences,
                "structure_score": structure_score,
                "recommendations": recommendations
            }

        except Exception as e:
            logger.error(f"❌ Failed to analyze content structure: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to analyze content structure: {str(e)}")

    def _calculate_structure_score(self, content: str, headings: List[dict], paragraphs: int) -> int:
        """Calculate content structure score"""
        score = 50  # Base score

        # Heading score
        if len(headings) >= 3:
            score += 20
        elif len(headings) >= 1:
            score += 10

        # Paragraph score
        if 3 <= paragraphs <= 10:
            score += 20
        elif paragraphs > 0:
            score += 10

        # Content length score
        word_count = len(content.split())
        if 300 <= word_count <= 2000:
            score += 10

        return min(score, 100)

    def _generate_structure_recommendations(self, content: str, headings: List[dict], paragraphs: int) -> List[str]:
        """Generate structure improvement recommendations"""
        recommendations = []

        if len(headings) < 2:
            recommendations.append("Añade más subtítulos (H2, H3) para mejorar la estructura")

        if paragraphs < 3:
            recommendations.append("Divide el contenido en más párrafos para mejor legibilidad")

        if paragraphs > 15:
            recommendations.append("Considera reducir el número de párrafos o usar subtítulos")

        word_count = len(content.split())
        if word_count < 300:
            recommendations.append("Aumenta la longitud del contenido para mejor SEO")

        if not any(marker in content for marker in ['•', '-', '1.', '2.']):
            recommendations.append("Añade listas para mejorar la organización del contenido")

        return recommendations
