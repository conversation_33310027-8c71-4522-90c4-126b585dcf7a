"""
Research Schemas
Pydantic models for SEO research operations
"""

from typing import Optional, List
from pydantic import BaseModel, Field


class ResearchRequest(BaseModel):
    """Request model for SEO research and keyword analysis"""
    topic: str = Field(..., description="Main topic/keyword to research")
    target_language: str = Field(default="es", description="Target language for analysis")
    include_reddit: bool = Field(default=True, description="Include Reddit insights")
    include_quora: bool = Field(default=True, description="Include Quora insights")
    # Project linking
    project_id: Optional[str] = Field(None, description="Project ID to link research to")
    # Geographic targeting
    target_country: Optional[str] = Field(default="ES", description="Target country code")
    # Additional sources
    include_news: bool = Field(default=False, description="Include news articles")
    # Competitive analysis
    competitor_domains: Optional[List[str]] = Field(default=[], description="Competitor domains to analyze")


class ResearchResponse(BaseModel):
    """Response model for research results"""
    status: str
    research_id: str
    topic: str
    target_language: str
    processing_time: float
    timestamp: float
    intent_analysis: dict
    google_results: dict
    social_insights: dict
    gpt_reference: dict
    entities_and_questions: dict
    content_opportunities: dict
    research_summary: dict
    research_quality_metrics: dict
    message: str

    class Config:
        from_attributes = True


class ResearchListResponse(BaseModel):
    """Response model for research list"""
    status: str
    research: List[dict]
    total: int
    limit: int
    offset: int
