"""
SEO GPT Optimizer Schemas
Pydantic models for request/response validation
"""

from .project import (
    ProjectCreateRequest,
    ProjectResponse,
    ProjectUpdateRequest
)
from .content import (
    ContentAnalysisRequest,
    ContentUpdateRequest,
    ProjectContentCreateRequest,
    ProjectContentUpdateRequest,
    ProjectContentResponse
)
from .research import (
    ResearchRequest,
    ResearchResponse
)
from .analysis import (
    ContentAnalysisResponse,
    SAIOAnalysisResponse
)
from .generation import (
    BlogGenerationRequest,
    SAIOContentRequest,
    ContentGenerationResponse
)

__all__ = [
    # Project schemas
    "ProjectCreateRequest",
    "ProjectResponse", 
    "ProjectUpdateRequest",
    # Content schemas
    "ContentAnalysisRequest",
    "ContentUpdateRequest",
    "ProjectContentCreateRequest",
    "ProjectContentUpdateRequest",
    "ProjectContentResponse",
    # Research schemas
    "ResearchRequest",
    "ResearchResponse",
    # Analysis schemas
    "ContentAnalysisResponse",
    "SAIOAnalysisResponse",
    # Generation schemas
    "BlogGenerationRequest",
    "SAIOContentRequest",
    "ContentGenerationResponse"
]
