"""
Project Management Schemas
Pydantic models for project CRUD operations
"""

from typing import Optional
from pydantic import BaseModel, Field
from app.db.seo_gpt_models import ContentType, ProjectStatus


class ProjectCreateRequest(BaseModel):
    """Request model for creating new SEO projects"""
    title: str = Field(..., description="Project title")
    description: Optional[str] = Field(None, description="Project description")
    topic: Optional[str] = Field(None, description="Main topic/keyword")
    target_language: str = Field(default="es", description="Target language")
    content_type: ContentType = Field(default=ContentType.ARTICLE, description="Type of content")
    target_gpt_rank_score: float = Field(default=80.0, description="Target GPT Rank score")
    user_id: Optional[str] = Field(None, description="User ID")


class ProjectUpdateRequest(BaseModel):
    """Request model for updating projects"""
    title: Optional[str] = Field(None, description="Project title")
    description: Optional[str] = Field(None, description="Project description")
    topic: Optional[str] = Field(None, description="Main topic/keyword")
    target_language: Optional[str] = Field(None, description="Target language")
    content_type: Optional[ContentType] = Field(None, description="Type of content")
    target_gpt_rank_score: Optional[float] = Field(None, description="Target GPT Rank score")
    status: Optional[ProjectStatus] = Field(None, description="Project status")
    content_text: Optional[str] = Field(None, description="Project content")


class ProjectResponse(BaseModel):
    """Response model for project data"""
    project_id: str
    title: str
    topic: Optional[str]
    status: str
    content_type: str
    target_language: str
    current_gpt_rank_score: float
    target_gpt_rank_score: float
    best_gpt_rank_score: float
    content_text: Optional[str]
    content_length: int
    word_count: int
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True


class ProjectListResponse(BaseModel):
    """Response model for project list"""
    status: str
    projects: list[ProjectResponse]
    total: int
    limit: int
    offset: int


class DashboardStatsResponse(BaseModel):
    """Response model for dashboard statistics"""
    status: str
    data: dict
