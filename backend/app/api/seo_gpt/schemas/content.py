"""
Content Management Schemas
Pydantic models for content operations
"""

from typing import Optional, List
from pydantic import BaseModel, Field


class ContentAnalysisRequest(BaseModel):
    """Request model for content SEO analysis"""
    project_id: str = Field(..., description="Project ID")
    content: str = Field(..., description="Content to analyze")
    target_keywords: Optional[List[str]] = Field(default=None, description="Target keywords for analysis")
    content_type: Optional[str] = Field(default="blog", description="Type of content")
    deep_analysis: bool = Field(default=False, description="Whether to perform deep contextual analysis")


class ContentUpdateRequest(BaseModel):
    """Request model for updating project content"""
    project_id: str = Field(..., description="Project ID")
    content: str = Field(..., description="Updated content")
    title: Optional[str] = Field(None, description="Updated title")


class ProjectContentCreateRequest(BaseModel):
    """Request model for creating project content"""
    title: str = Field(..., description="Content title")
    content_type: str = Field(default="blog", description="Type of content: blog, article, research, guide")
    content_text: Optional[str] = Field(None, description="Content text")
    content_html: Optional[str] = Field(None, description="Content HTML")
    target_keywords: Optional[List[str]] = Field(default=[], description="Target keywords")
    meta_description: Optional[str] = Field(None, description="Meta description")
    slug: Optional[str] = Field(None, description="URL slug")


class ProjectContentUpdateRequest(BaseModel):
    """Request model for updating project content"""
    title: Optional[str] = Field(None, description="Content title")
    content_text: Optional[str] = Field(None, description="Content text")
    content_html: Optional[str] = Field(None, description="Content HTML")
    status: Optional[str] = Field(None, description="Content status: draft, published, archived")
    target_keywords: Optional[List[str]] = Field(None, description="Target keywords")
    meta_description: Optional[str] = Field(None, description="Meta description")
    slug: Optional[str] = Field(None, description="URL slug")
    current_gpt_rank_score: Optional[float] = Field(None, description="Current GPT Rank score")
    seo_score: Optional[float] = Field(None, description="SEO score")


class ProjectContentResponse(BaseModel):
    """Response model for project content"""
    content_id: str
    project_id: str
    title: str
    content_type: str
    content_text: Optional[str]
    content_html: Optional[str]
    status: str
    target_keywords: Optional[List[str]]
    meta_description: Optional[str]
    slug: Optional[str]
    current_gpt_rank_score: float
    seo_score: float
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True


class ContentListResponse(BaseModel):
    """Response model for content list"""
    status: str
    contents: List[ProjectContentResponse]
    total: int
    limit: int
    offset: int
