"""
Analysis Schemas
Pydantic models for content analysis operations
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class ContentAnalysisResponse(BaseModel):
    """Response model for content analysis"""
    status: str
    analysis: Dict[str, Any]
    project_id: Optional[str] = None
    analysis_type: Optional[str] = None
    gpt_rank_score: Optional[float] = None
    score_grade: Optional[str] = None
    improvement_suggestions: Optional[List[str]] = None
    content_stats: Optional[Dict[str, Any]] = None
    component_scores: Optional[Dict[str, Any]] = None
    weaknesses_identified: Optional[int] = None
    contextual_insights: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True


class SAIOAnalysisResponse(BaseModel):
    """Response model for SAIO analysis"""
    status: str
    project_id: str
    message: str
    saio_results: Dict[str, Any]
    analysis_type: str
    world_first: bool

    class Config:
        from_attributes = True


class ConfidenceAnalysisResponse(BaseModel):
    """Response model for LLM confidence analysis"""
    status: str
    analysis_type: str
    project_id: str
    overall_confidence: Dict[str, Any]
    weaknesses_analysis: List[Dict[str, Any]]
    quality_metrics: Dict[str, Any]
    analysis_metadata: Dict[str, Any]

    class Config:
        from_attributes = True


class ScoreResponse(BaseModel):
    """Response model for project score"""
    status: str
    project_id: str
    current_score: float
    best_score: float
    target_score: float
    score_grade: str
    confidence_level: str
    latest_analysis: Optional[Dict[str, Any]]
    score_history: List[Dict[str, Any]]

    class Config:
        from_attributes = True
