"""
Content Generation Schemas
Pydantic models for content generation operations
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class BlogGenerationRequest(BaseModel):
    """Request model for AI blog content generation"""
    project_id: str = Field(..., description="Project ID")
    topic: str = Field(..., description="Blog topic")
    content_type: str = Field(default="educational", description="Type of content (educational, motivational, balanced)")
    target_length: str = Field(default="medium", description="Target length (short, medium, long)")


class SAIOContentRequest(BaseModel):
    """Request model for SAIO-optimized content generation"""
    project_id: str = Field(..., description="Project ID")
    topic: str = Field(..., description="Content topic")
    content_type: str = Field(default="how_to", description="SAIO template type (how_to, list_article, comparison)")
    optimize_for: List[str] = Field(default=["chatgpt", "perplexity", "google_sge"], description="AI platforms to optimize for")


class ContentGenerationResponse(BaseModel):
    """Response model for content generation"""
    status: str
    project_id: str
    message: str
    data: Dict[str, Any]

    class Config:
        from_attributes = True


class SAIOContentResponse(BaseModel):
    """Response model for SAIO content generation"""
    status: str
    project_id: str
    message: str
    data: Dict[str, Any]
    world_first: bool
    research_based: bool

    class Config:
        from_attributes = True
