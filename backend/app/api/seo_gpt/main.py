"""
SEO GPT Optimizer Main Router
Main FastAPI application that mounts all sub-routers
"""

import logging
from fastapi import FastAPI, APIRouter

from .routers import (
    projects_router,
    contents_router,
    research_router,
    analysis_router,
    generation_router,
    suggestions_router
)

logger = logging.getLogger(__name__)

# Create main router for SEO GPT Optimizer
router = APIRouter()

# Mount all sub-routers with appropriate prefixes and tags
router.include_router(
    projects_router, 
    prefix="/projects", 
    tags=["projects"]
)

router.include_router(
    contents_router, 
    prefix="/projects", 
    tags=["contents"]
)

router.include_router(
    research_router, 
    tags=["research"]
)

router.include_router(
    analysis_router, 
    prefix="/content", 
    tags=["analysis"]
)

router.include_router(
    generation_router, 
    prefix="/content", 
    tags=["generation"]
)



router.include_router(
    suggestions_router,
    prefix="/suggestions",
    tags=["suggestions"]
)

# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check endpoint for SEO GPT Optimizer"""
    return {
        "status": "healthy",
        "service": "SEO GPT Optimizer",
        "version": "2.0.0",
        "refactored": True
    }

# Root endpoint with service info
@router.get("/")
async def root():
    """Root endpoint with service information"""
    return {
        "service": "SEO GPT Optimizer API",
        "version": "2.0.0",
        "description": "Refactored SEO & GPT Optimizer with modular architecture",
        "endpoints": {
            "projects": "/projects",
            "contents": "/projects/{project_id}/contents",
            "research": "/research",
            "analysis": "/content/analyze",
            "generation": "/content/generate-blog",
            "suggestions": "/suggestions"
        },
        "features": [
            "Project Management",
            "Content Analysis",
            "SEO Research",
            "AI Content Generation",
            "SAIO Optimization",
            "Smart Suggestions",
            "Batch Operations"
        ]
    }

logger.info("✅ SEO GPT Optimizer main router initialized with all sub-routers")
