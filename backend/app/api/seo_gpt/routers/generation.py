"""
Generation Router
FastAPI router for content generation operations
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.db.session import get_db
from ..services.generation_service import GenerationService
from ..schemas.generation import (
    BlogGenerationRequest,
    SAIOContentRequest,
    ContentGenerationResponse,
    SAIOContentResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/generate-blog", response_model=dict)
async def generate_blog_content(
    request: BlogGenerationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Generate complete blog content using AI content generation.
    
    This endpoint creates a full blog post including:
    - AI-generated content using Gemini/GPT-4
    - Contextual images with Ideogram
    - Real-time GPT Rank analysis
    - Structured blog format
    """
    try:
        service = GenerationService(db)
        result = await service.generate_blog_content(request)
        
        return JSONResponse(
            status_code=200,
            content=result.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Blog generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Blog generation failed: {str(e)}")


@router.post("/generate-saio", response_model=dict)
async def generate_saio_content(
    request: SAIOContentRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Generate SAIO-optimized content - World's first real SAIO content generator.
    
    Uses research-backed templates and optimization strategies for:
    - Google SGE
    - ChatGPT
    - Perplexity
    - Bing Chat
    """
    try:
        service = GenerationService(db)
        result = await service.generate_saio_content(request)
        
        return JSONResponse(
            status_code=200,
            content=result.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ SAIO content generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"SAIO content generation failed: {str(e)}")
