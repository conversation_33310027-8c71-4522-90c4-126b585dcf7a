"""
Contents Router
FastAPI router for content management operations
"""

import logging
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.db.session import get_db
from ..services.content_service import ContentService
from ..schemas.content import (
    ProjectContentCreateRequest,
    ProjectContentUpdateRequest,
    ProjectContentResponse,
    ContentListResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/{project_id}/contents", response_model=dict)
async def get_project_contents(
    project_id: str,
    content_type: Optional[str] = None,
    status: Optional[str] = None,
    limit: int = 20,
    offset: int = 0,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get all content items for a specific project."""
    try:
        service = ContentService(db)
        result = service.get_project_contents(project_id, content_type, status, limit, offset)
        
        return JSONResponse(
            status_code=200,
            content=result.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get project contents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get project contents: {str(e)}")


@router.post("/{project_id}/contents", response_model=dict)
async def create_project_content(
    project_id: str,
    request: ProjectContentCreateRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Create a new content item within a project."""
    try:
        service = ContentService(db)
        content = service.create_project_content(project_id, request)
        
        return JSONResponse(
            status_code=201,
            content={
                "status": "success",
                "data": {
                    "content_id": content.content_id,
                    "title": content.title,
                    "content_type": content.content_type,
                    "status": content.status,
                    "word_count": content.current_gpt_rank_score,  # This seems wrong in original, should be word_count
                    "created_at": content.created_at
                },
                "message": "Content created successfully"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error creating content in project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating content: {str(e)}")


@router.get("/{project_id}/contents/{content_id}", response_model=dict)
async def get_project_content(
    project_id: str,
    content_id: str,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get a specific content item from a project."""
    try:
        service = ContentService(db)
        content = service.get_project_content(project_id, content_id)
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": content.dict()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get content: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get content: {str(e)}")


@router.patch("/{project_id}/contents/{content_id}", response_model=dict)
async def update_project_content(
    project_id: str,
    content_id: str,
    request: ProjectContentUpdateRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Update a specific content item in a project."""
    try:
        logger.info(f"🔄 PATCH REQUEST - Starting update process")
        logger.info(f"🔄 PATCH REQUEST - project_id: {project_id}")
        logger.info(f"🔄 PATCH REQUEST - content_id: {content_id}")
        logger.info(f"🔄 PATCH REQUEST - request data: {request.model_dump()}")
        
        service = ContentService(db)
        content = service.update_project_content(project_id, content_id, request)
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": content.dict(),
                "message": "Content updated successfully"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to update content: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update content: {str(e)}")


@router.delete("/{project_id}/contents/{content_id}", status_code=204)
async def delete_project_content(
    project_id: str,
    content_id: str,
    db: Session = Depends(get_db)
):
    """Delete a content item from a project."""
    try:
        service = ContentService(db)
        service.delete_project_content(project_id, content_id)
        
        # Return 204 No Content for successful deletion
        return None
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to delete content: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete content: {str(e)}")
