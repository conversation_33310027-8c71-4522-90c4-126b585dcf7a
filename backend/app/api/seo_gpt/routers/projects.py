"""
Projects Router
FastAPI router for project CRUD operations
"""

import logging
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.db.seo_gpt_models import ProjectStatus
from ..services.project_service import ProjectService
from ..schemas.project import (
    ProjectCreateRequest,
    ProjectUpdateRequest,
    ProjectResponse,
    ProjectListResponse,
    DashboardStatsResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/create", response_model=dict)
async def create_project_legacy(
    request: ProjectCreateRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Create a new SEO GPT Optimizer project (legacy endpoint).

    This endpoint creates a new project for organizing content generation
    and analysis work within the SEO GPT Optimizer.
    """
    try:
        service = ProjectService(db)
        project = service.create_project(request)

        return JSONResponse(
            status_code=201,
            content={
                "status": "success",
                "project_id": project.project_id,
                "message": "Project created successfully"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Project creation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Project creation failed: {str(e)}")


@router.post("/", response_model=dict)
async def create_project(
    request: ProjectCreateRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Create a new SEO GPT Optimizer project.
    
    This endpoint creates a new project for organizing content generation
    and analysis work within the SEO GPT Optimizer.
    """
    try:
        service = ProjectService(db)
        project = service.create_project(request)
        
        return JSONResponse(
            status_code=201,
            content={
                "status": "success",
                "data": {
                    "project_id": project.project_id,
                    "project": project.dict()
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Project creation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Project creation failed: {str(e)}")


@router.get("/{project_id}", response_model=dict)
async def get_project(
    project_id: str,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get a specific SEO GPT Optimizer project."""
    try:
        service = ProjectService(db)
        project = service.get_project(project_id)
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": project.dict()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get project: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get project: {str(e)}")


@router.patch("/{project_id}", response_model=dict)
async def update_project(
    project_id: str,
    request: ProjectUpdateRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Update a specific SEO GPT Optimizer project."""
    try:
        service = ProjectService(db)
        project = service.update_project(project_id, request)
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": project.dict()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to update project: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update project: {str(e)}")


@router.delete("/{project_id}", response_model=dict)
async def delete_project(
    project_id: str,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Delete a specific SEO GPT Optimizer project."""
    try:
        service = ProjectService(db)
        result = service.delete_project(project_id)
        
        return JSONResponse(
            status_code=200,
            content=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to delete project: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete project: {str(e)}")


@router.get("/", response_model=dict)
async def list_projects(
    user_id: Optional[str] = None,
    status: Optional[ProjectStatus] = None,
    limit: int = 20,
    offset: int = 0,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """List SEO GPT Optimizer projects."""
    try:
        service = ProjectService(db)
        result = service.list_projects(user_id, status, limit, offset)
        
        return JSONResponse(
            status_code=200,
            content=result.dict()
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to list projects: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list projects: {str(e)}")


@router.get("/dashboard/stats", response_model=dict)
async def get_dashboard_stats(
    user_id: Optional[str] = None,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get dashboard analytics statistics."""
    try:
        service = ProjectService(db)
        result = service.get_dashboard_stats(user_id)
        
        return JSONResponse(
            status_code=200,
            content=result.dict()
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to get dashboard stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard stats: {str(e)}")


@router.get("/analytics/score-trends", response_model=dict)
async def get_score_trends(
    user_id: Optional[str] = None,
    days: int = 30,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get score trends over time."""
    try:
        service = ProjectService(db)
        result = service.get_score_trends(user_id, days)

        return JSONResponse(
            status_code=200,
            content=result
        )

    except Exception as e:
        logger.error(f"❌ Failed to get score trends: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get score trends: {str(e)}")


@router.get("/search", response_model=dict)
async def search_projects(
    q: str,
    status: Optional[str] = None,
    content_type: Optional[str] = None,
    limit: int = 20,
    offset: int = 0,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Search projects by query string."""
    try:
        service = ProjectService(db)
        result = service.search_projects(q, status, content_type, limit, offset)

        return JSONResponse(
            status_code=200,
            content=result
        )

    except Exception as e:
        logger.error(f"❌ Failed to search projects: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to search projects: {str(e)}")
