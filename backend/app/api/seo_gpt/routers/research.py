"""
Research Router
FastAPI router for research operations
"""

import logging
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List
import google.generativeai as genai
from pydantic import BaseModel

from app.db.session import get_db
from app.core.config import get_settings
from ..services.research_service import ResearchService
from ..schemas.research import (
    ResearchRequest,
    ResearchResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()

# Schemas for keyword generation
class KeywordGenerationRequest(BaseModel):
    topic: str
    language: str = "es"

class KeywordGenerationResponse(BaseModel):
    status: str
    keywords: List[str]
    message: str


@router.post("/research", response_model=dict)
async def conduct_research(
    request: ResearchRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Conduct comprehensive research for a topic.
    
    This endpoint initiates a comprehensive research process including:
    - Search intent analysis
    - Google Top 10 + Reddit + Quora scraping
    - GPT reference responses
    - Entity extraction and common questions
    """
    try:
        service = ResearchService(db)
        result = await service.conduct_research(request)
        
        return JSONResponse(
            status_code=200,
            content=result.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Research failed for topic '{request.topic}': {str(e)}")
        raise HTTPException(status_code=500, detail=f"Research failed: {str(e)}")


@router.get("/projects/{project_id}/research", response_model=dict)
async def get_project_research(
    project_id: str,
    limit: int = 20,
    offset: int = 0,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get research data for a specific project."""
    try:
        service = ResearchService(db)
        result = service.get_project_research(project_id, limit, offset)
        
        return JSONResponse(
            status_code=200,
            content=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get project research: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get project research: {str(e)}")


@router.post("/generate-keywords", response_model=KeywordGenerationResponse)
async def generate_intelligent_keywords(
    request: KeywordGenerationRequest
) -> JSONResponse:
    """
    Generate intelligent blog topics using Gemini AI for content strategy.

    This endpoint uses AI to generate high-quality, relevant blog topics that are:
    - Semantically related to the main topic
    - Optimized for blog article creation
    - Focused on informational content
    - Varied in scope and specificity
    """
    try:
        settings = get_settings()

        if not settings.GEMINI_API_KEY:
            # Fallback to simple topic generation
            fallback_topics = _generate_fallback_keywords(request.topic)
            return JSONResponse(
                status_code=200,
                content={
                    "status": "success",
                    "keywords": fallback_topics,
                    "message": "Blog topics generated using fallback method (Gemini not available)"
                }
            )

        # Initialize Gemini
        genai.configure(api_key=settings.GEMINI_API_KEY)
        model = genai.GenerativeModel('gemini-1.5-flash')

        # Create intelligent prompt for blog topic generation
        prompt = f"""
        Genera 15 temas inteligentes y relevantes para crear artículos de blog sobre: "{request.topic}"

        Criterios importantes:
        1. Enfócate en temas que generen artículos informativos y útiles
        2. Incluye variaciones específicas y detalladas (3-8 palabras)
        3. Considera diferentes ángulos y perspectivas del tema principal
        4. Prioriza temas que un blogger puede desarrollar completamente
        5. Incluye preguntas y problemas que la gente realmente busca resolver
        6. Mezcla temas amplios y específicos
        7. Considera subtemas y aspectos relacionados

        Ejemplos del tipo de temas que necesito:
        - "Cómo [hacer algo específico relacionado con el tema]"
        - "Qué necesitas saber sobre [aspecto del tema]"
        - "Beneficios y desventajas de [tema]"
        - "Guía completa para [proceso relacionado con tema]"
        - "[Tema] para principiantes: todo lo que debes saber"
        - "Mejores estrategias para [objetivo relacionado con tema]"
        - "Errores comunes en [tema] y cómo evitarlos"

        Responde SOLO con los 15 temas separados por comas, sin numeración ni explicaciones.
        """

        # Generate keywords with Gemini
        response = model.generate_content(prompt)

        if not response.text:
            raise Exception("Empty response from Gemini")

        # Parse keywords from response
        keywords_text = response.text.strip()
        keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]

        # Clean and validate keywords
        cleaned_keywords = []
        for keyword in keywords[:15]:  # Limit to 15
            cleaned = keyword.strip().strip('"').strip("'")
            if len(cleaned) > 3 and len(cleaned) < 100:  # Reasonable length
                cleaned_keywords.append(cleaned)

        if len(cleaned_keywords) < 5:
            # If we don't have enough good keywords, supplement with fallback
            fallback_keywords = _generate_fallback_keywords(request.topic)
            cleaned_keywords.extend(fallback_keywords[:15-len(cleaned_keywords)])

        logger.info(f"✅ Generated {len(cleaned_keywords)} intelligent blog topics for topic: {request.topic}")

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "keywords": cleaned_keywords,
                "message": f"Generated {len(cleaned_keywords)} intelligent blog topics using Gemini AI"
            }
        )

    except Exception as e:
        logger.error(f"❌ Keyword generation failed: {str(e)}")

        # Fallback to simple topic generation
        fallback_topics = _generate_fallback_keywords(request.topic)
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "keywords": fallback_topics,
                "message": f"Blog topics generated using fallback method (AI error: {str(e)})"
            }
        )


def _generate_fallback_keywords(topic: str) -> List[str]:
    """Generate fallback blog topics when AI is not available."""
    templates = [
        f"Qué es {topic} y por qué es importante",
        f"Cómo implementar {topic} paso a paso",
        f"Beneficios y desventajas de {topic}",
        f"Guía completa de {topic} para principiantes",
        f"{topic} para principiantes: todo lo que necesitas saber",
        f"Mejores estrategias para {topic}",
        f"{topic} paso a paso: tutorial completo",
        f"Todo lo que debes saber sobre {topic}",
        f"{topic} explicado de forma sencilla",
        f"Consejos expertos para {topic}",
        f"Cómo dominar {topic} fácilmente",
        f"Aprende {topic} desde cero",
        f"{topic} completo: guía definitiva",
        f"Por qué {topic} es esencial hoy en día",
        f"Errores comunes en {topic} y cómo evitarlos"
    ]

    return templates[:15]
