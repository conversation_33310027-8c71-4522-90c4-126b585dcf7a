"""
Analysis Router
FastAPI router for content analysis operations
"""

import logging
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.db.session import get_db
from ..services.analysis_service import AnalysisService
from ..schemas.content import ContentAnalysisRequest
from ..schemas.analysis import (
    ContentAnalysisResponse,
    SAIOAnalysisResponse,
    ConfidenceAnalysisResponse,
    ScoreResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/analyze", response_model=dict)
async def analyze_content_seo(
    request: ContentAnalysisRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Analyze content for SEO and SAIO optimization.
    
    This endpoint provides real-time content analysis including:
    - SEO score and keyword analysis
    - SAIO optimization score
    - Readability analysis
    - Content structure analysis
    - Optimization suggestions
    """
    try:
        service = AnalysisService(db)
        result = await service.analyze_content_seo(request)
        
        return JSONResponse(
            status_code=200,
            content=result.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Content analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Content analysis failed: {str(e)}")


@router.post("/confidence-analysis", response_model=dict)
async def analyze_content_confidence(
    request: ContentAnalysisRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Analyze content confidence for LLM ranking (Perplexity, ChatGPT, Claude, etc.)
    
    This endpoint is triggered manually when user clicks the LLM confidence analysis button.
    Evaluates how well content will rank in AI-powered search engines and LLMs.
    
    Returns:
    - LLM ranking confidence score with visual badge
    - LLM-specific weakness analysis
    - Improvement recommendations for AI systems
    - Priority optimizations for better LLM performance
    """
    try:
        service = AnalysisService(db)
        result = await service.analyze_content_confidence(request)
        
        return JSONResponse(
            status_code=200,
            content=result.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ LLM confidence analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"LLM confidence analysis failed: {str(e)}")


@router.post("/analyze-saio", response_model=dict)
async def analyze_saio_content(
    request: ContentAnalysisRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Analyze content with SAIO Agent - the world's first real SAIO optimizer.
    
    Based on comprehensive research:
    - 73% similarity between ChatGPT and Bing results
    - 100% of Perplexity citations have images
    - 90% of YMYL citations are lists/how-tos
    - E-E-A-T crucial for AI trust
    """
    try:
        service = AnalysisService(db)
        result = await service.analyze_saio_content(request)
        
        return JSONResponse(
            status_code=200,
            content=result.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ SAIO analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"SAIO analysis failed: {str(e)}")


@router.post("/batch-analyze", response_model=dict)
async def batch_analyze_projects(
    project_ids: List[str],
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Batch analyze multiple projects.

    This endpoint allows analyzing multiple projects at once for
    performance optimization and bulk operations.
    """
    try:
        service = AnalysisService(db)
        results = await service.batch_analyze_projects(project_ids)

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "results": results,
                "analyzed_count": len(results),
                "requested_count": len(project_ids)
            }
        )

    except Exception as e:
        logger.error(f"❌ Batch analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Batch analysis failed: {str(e)}")


@router.post("/content-structure", response_model=dict)
async def analyze_content_structure(
    content: str,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Analyze content structure for headings, paragraphs, and organization.

    This endpoint provides detailed structure analysis for content optimization.
    """
    try:
        service = AnalysisService(db)
        result = await service.analyze_content_structure(content)

        return JSONResponse(
            status_code=200,
            content=result
        )

    except Exception as e:
        logger.error(f"❌ Content structure analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Content structure analysis failed: {str(e)}")


@router.get("/projects/{project_id}/score", response_model=dict)
async def get_project_score(
    project_id: str,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get current GPT Rank Score for a project."""
    try:
        service = AnalysisService(db)
        result = service.get_project_score(project_id)
        
        return JSONResponse(
            status_code=200,
            content=result.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get project score: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get project score: {str(e)}")
