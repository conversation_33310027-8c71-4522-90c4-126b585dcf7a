"""
Suggestions Router
FastAPI router for content suggestions and recommendations
"""

import logging
from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.db.session import get_db
from ..services.suggestions_service import SuggestionsService

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/topics", response_model=dict)
async def get_topic_suggestions(
    q: str = Query(..., description="Partial topic to get suggestions for"),
    limit: int = Query(10, description="Maximum number of suggestions"),
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Get topic suggestions based on partial input.
    
    This endpoint provides intelligent topic suggestions for content creation
    based on trending topics, user history, and SEO opportunities.
    """
    try:
        service = SuggestionsService(db)
        suggestions = await service.get_topic_suggestions(q, limit)
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "suggestions": suggestions,
                "query": q,
                "count": len(suggestions)
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to get topic suggestions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get topic suggestions: {str(e)}")


@router.get("/keywords", response_model=dict)
async def get_keyword_suggestions(
    topic: str = Query(..., description="Topic to get keyword suggestions for"),
    limit: int = Query(20, description="Maximum number of keywords"),
    language: str = Query("es", description="Target language"),
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Get keyword suggestions for a specific topic.
    
    This endpoint provides SEO-optimized keyword suggestions based on:
    - Search volume data
    - Competition analysis
    - Semantic relevance
    - SAIO optimization potential
    """
    try:
        service = SuggestionsService(db)
        keywords = await service.get_keyword_suggestions(topic, limit, language)
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "keywords": keywords,
                "topic": topic,
                "language": language,
                "count": len(keywords)
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to get keyword suggestions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get keyword suggestions: {str(e)}")


@router.post("/content-optimization", response_model=dict)
async def get_content_optimization_suggestions(
    content: str,
    target_keywords: List[str] = [],
    content_type: str = "blog",
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Get content optimization suggestions.
    
    This endpoint analyzes content and provides specific optimization
    recommendations for SEO and SAIO performance.
    """
    try:
        service = SuggestionsService(db)
        suggestions = await service.get_content_optimization_suggestions(
            content, target_keywords, content_type
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "suggestions": suggestions,
                "content_length": len(content),
                "target_keywords": target_keywords
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to get content optimization suggestions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get content optimization suggestions: {str(e)}")
