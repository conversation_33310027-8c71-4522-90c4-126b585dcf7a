"""
Pydantic schemas for Runway ML API requests and responses.
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field

class ReferenceImage(BaseModel):
    """Reference image for image generation."""
    uri: str = Field(..., description="HTTPS URL or data URI of the reference image")
    tag: str = Field(..., description="Tag to reference the image in prompt (3-16 chars, alphanumeric + underscore)")

class TextToImageRequest(BaseModel):
    """Request schema for text-to-image generation."""
    prompt_text: str = Field(..., max_length=1000, description="Text description of the image to generate")
    ratio: str = Field(
        default="1280:720",
        description="Image aspect ratio",
        pattern="^(1920:1080|1080:1920|1024:1024|1360:768|1080:1080|1168:880|1440:1080|1080:1440|1808:768|2112:912|1280:720|720:1280|720:720|960:720|720:960|1680:720)$"
    )
    seed: Optional[int] = Field(None, ge=0, le=4294967295, description="Random seed for reproducible results")
    reference_images: Optional[List[ReferenceImage]] = Field(None, max_items=3, description="Reference images (max 3)")

class ImageToVideoRequest(BaseModel):
    """Request schema for image-to-video generation."""
    prompt_image: str = Field(..., description="HTTPS URL or data URI of the source image")
    prompt_text: str = Field(default="Generate a video", description="Text description for video generation")
    ratio: str = Field(
        default="1280:720",
        description="Video aspect ratio",
        pattern="^(1280:720|720:1280|1104:832|832:1104|960:960|1584:672)$"
    )
    duration: int = Field(default=5, description="Video duration in seconds")

class RunwayResponse(BaseModel):
    """Base response schema for Runway ML operations."""
    success: bool = Field(..., description="Whether the operation was successful")
    task_id: Optional[str] = Field(None, description="Runway task ID")
    status: Optional[str] = Field(None, description="Task status")
    output: Optional[List[str]] = Field(None, description="Generated content URLs")
    error: Optional[str] = Field(None, description="Error message if operation failed")
    details: Optional[str] = Field(None, description="Additional error details")

class ImageGenerationResponse(RunwayResponse):
    """Response schema for image generation."""
    image_url: Optional[str] = Field(None, description="URL of the generated image")

class VideoGenerationResponse(RunwayResponse):
    """Response schema for video generation."""
    video_url: Optional[str] = Field(None, description="URL of the generated video")

class RunwayWorkflowRequest(BaseModel):
    """Request schema for complete image-to-video workflow."""
    prompt_text: str = Field(..., max_length=1000, description="Text description for image generation")
    image_ratio: str = Field(
        default="1280:720",
        description="Image aspect ratio",
        pattern="^(1920:1080|1080:1920|1024:1024|1360:768|1080:1080|1168:880|1440:1080|1080:1440|1808:768|2112:912|1280:720|720:1280|720:720|960:720|720:960|1680:720)$"
    )
    video_prompt: str = Field(default="Generate a video", description="Text description for video generation")
    video_ratio: str = Field(
        default="1280:720",
        description="Video aspect ratio",
        pattern="^(1280:720|720:1280|1104:832|832:1104|960:960|1584:672)$"
    )
    video_duration: int = Field(default=5, description="Video duration in seconds")
    seed: Optional[int] = Field(None, ge=0, le=4294967295, description="Random seed for reproducible results")

class RunwayWorkflowResponse(BaseModel):
    """Response schema for complete workflow."""
    success: bool = Field(..., description="Whether the workflow was successful")
    image_result: Optional[ImageGenerationResponse] = Field(None, description="Image generation result")
    video_result: Optional[VideoGenerationResponse] = Field(None, description="Video generation result")
    error: Optional[str] = Field(None, description="Error message if workflow failed")
    details: Optional[str] = Field(None, description="Additional error details")

class MediaDownloadRequest(BaseModel):
    """Request schema for downloading media files."""
    url: str = Field(..., description="URL of the media file to download")
    filename: str = Field(..., description="Desired filename for the download")
