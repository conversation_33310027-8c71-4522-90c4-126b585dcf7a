/**
 * Runway ML Service for image and video generation
 * 
 * This service provides functionality to:
 * 1. Generate images from text prompts
 * 2. Convert images to videos
 * 3. Complete workflow (text -> image -> video)
 */

import axios from "axios";

const API_BASE_URL = "/api/runway";

// Types for Runway ML API
export interface ReferenceImage {
  uri: string;
  tag: string;
}

export interface TextToImageRequest {
  prompt_text: string;
  ratio?: string;
  seed?: number;
  reference_images?: ReferenceImage[];
}

export interface ImageToVideoRequest {
  prompt_image: string;
  prompt_text?: string;
  ratio?: string;
  duration?: number;
}

export interface RunwayWorkflowRequest {
  prompt_text: string;
  image_ratio?: string;
  video_prompt?: string;
  video_ratio?: string;
  video_duration?: number;
  seed?: number;
}

export interface RunwayResponse {
  success: boolean;
  task_id?: string;
  status?: string;
  output?: string[];
  error?: string;
  details?: string;
}

export interface ImageGenerationResponse extends RunwayResponse {
  image_url?: string;
}

export interface VideoGenerationResponse extends RunwayResponse {
  video_url?: string;
}

export interface RunwayWorkflowResponse {
  success: boolean;
  image_result?: ImageGenerationResponse;
  video_result?: VideoGenerationResponse;
  error?: string;
  details?: string;
}

export interface RunwayStatus {
  service: string;
  status: string;
  api_key_configured: boolean;
  models: {
    image_generation: string;
    video_generation: string;
  };
  supported_ratios: {
    image: string[];
    video: string[];
  };
  video_durations: number[];
}

/**
 * Generate an image from text using Runway's gen4_image model
 */
export const generateImage = async (
  request: TextToImageRequest
): Promise<ImageGenerationResponse> => {
  try {
    console.log("🎨 Generating image with Runway ML:", request.prompt_text.substring(0, 100));
    
    const response = await axios.post(`${API_BASE_URL}/text-to-image`, request);
    
    if (response.data.success) {
      console.log("✅ Image generated successfully:", response.data.image_url);
    } else {
      console.error("❌ Image generation failed:", response.data.error);
    }
    
    return response.data;
  } catch (error) {
    console.error("❌ Error generating image:", error);
    
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.detail || "Failed to generate image");
    }
    
    throw new Error("Network error occurred while generating image");
  }
};

/**
 * Convert an image to video using Runway's gen4_turbo model
 */
export const generateVideo = async (
  request: ImageToVideoRequest
): Promise<VideoGenerationResponse> => {
  try {
    console.log("🎬 Converting image to video with Runway ML");
    
    const response = await axios.post(`${API_BASE_URL}/image-to-video`, request);
    
    if (response.data.success) {
      console.log("✅ Video generated successfully:", response.data.video_url);
    } else {
      console.error("❌ Video generation failed:", response.data.error);
    }
    
    return response.data;
  } catch (error) {
    console.error("❌ Error generating video:", error);
    
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.detail || "Failed to generate video");
    }
    
    throw new Error("Network error occurred while generating video");
  }
};

/**
 * Complete workflow: Generate image from text, then convert to video
 */
export const runCompleteWorkflow = async (
  request: RunwayWorkflowRequest
): Promise<RunwayWorkflowResponse> => {
  try {
    console.log("🚀 Starting complete Runway ML workflow:", request.prompt_text.substring(0, 100));
    
    const response = await axios.post(`${API_BASE_URL}/workflow`, request);
    
    if (response.data.success) {
      console.log("✅ Workflow completed successfully");
      console.log("📸 Image URL:", response.data.image_result?.image_url);
      console.log("🎬 Video URL:", response.data.video_result?.video_url);
    } else {
      console.error("❌ Workflow failed:", response.data.error);
    }
    
    return response.data;
  } catch (error) {
    console.error("❌ Error in workflow:", error);
    
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.detail || "Workflow failed");
    }
    
    throw new Error("Network error occurred during workflow");
  }
};

/**
 * Get Runway ML service status
 */
export const getRunwayStatus = async (): Promise<RunwayStatus> => {
  try {
    const response = await axios.get(`${API_BASE_URL}/status`);
    return response.data;
  } catch (error) {
    console.error("❌ Error getting Runway status:", error);
    throw new Error("Failed to get service status");
  }
};

/**
 * Utility function to convert a File to data URI
 */
export const fileToDataUri = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

/**
 * Predefined aspect ratios for images
 */
export const IMAGE_RATIOS = [
  { label: "Landscape HD (1280:720)", value: "1280:720" },
  { label: "Portrait HD (720:1280)", value: "720:1280" },
  { label: "Square (1024:1024)", value: "1024:1024" },
  { label: "Widescreen (1360:768)", value: "1360:768" },
  { label: "Instagram Square (1080:1080)", value: "1080:1080" },
  { label: "Instagram Portrait (1080:1440)", value: "1080:1440" },
  { label: "Ultra Wide (1680:720)", value: "1680:720" }
];

/**
 * Predefined aspect ratios for videos
 */
export const VIDEO_RATIOS = [
  { label: "Landscape HD (1280:720)", value: "1280:720" },
  { label: "Portrait HD (720:1280)", value: "720:1280" },
  { label: "Instagram (1104:832)", value: "1104:832" },
  { label: "Instagram Portrait (832:1104)", value: "832:1104" },
  { label: "Square (960:960)", value: "960:960" },
  { label: "Cinematic (1584:672)", value: "1584:672" }
];

/**
 * Available video durations
 */
export const VIDEO_DURATIONS = [
  { label: "5 segundos", value: 5 },
  { label: "10 segundos", value: 10 }
];

// Default export with all functions
const RunwayService = {
  generateImage,
  generateVideo,
  runCompleteWorkflow,
  getRunwayStatus,
  fileToDataUri,
  IMAGE_RATIOS,
  VIDEO_RATIOS,
  VIDEO_DURATIONS
};

export default RunwayService;
