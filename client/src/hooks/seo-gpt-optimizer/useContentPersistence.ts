/**
 * Content Persistence Hook
 * Manages content saving to project context with localStorage backup
 */

import { useState, useCallback, useEffect } from 'react';

export interface ContentPersistenceConfig {
  projectId: string;
  autoSaveInterval?: number;
  enableLocalStorage?: boolean;
}

export interface ContentState {
  content: string;
  topic: string;
  lastSaved?: Date;
  isDirty: boolean;
  isSaving: boolean;
  error?: string;
}

export interface ContentMetadata {
  wordCount: number;
  charCount: number;
  readingTime: number;
  lastModified: Date;
}

export const useContentPersistence = (config: ContentPersistenceConfig) => {
  const [state, setState] = useState<ContentState>({
    content: '',
    topic: '',
    isDirty: false,
    isSaving: false
  });

  const localStorageKey = `content-builder-${config.projectId}`;

  // Load initial content
  useEffect(() => {
    const loadContent = async () => {
      try {
        // Try to load from server first
        const API_BASE = import.meta.env.VITE_API_BASE || "http://localhost:8001";
        const response = await fetch(`${API_BASE}/api/v1/seo-gpt/projects/${config.projectId}`);
        
        if (response.ok) {
          const project = await response.json();
          setState(prev => ({
            ...prev,
            content: project.data?.content_text || '',
            topic: project.data?.title || '',
            lastSaved: project.data?.updated_at ? new Date(project.data.updated_at) : undefined
          }));
          return;
        }
      } catch (error) {
        console.warn('Failed to load from server, trying localStorage:', error);
      }

      // Fallback to localStorage
      if (config.enableLocalStorage !== false) {
        try {
          const stored = localStorage.getItem(localStorageKey);
          if (stored) {
            const data = JSON.parse(stored);
            setState(prev => ({
              ...prev,
              content: data.content || '',
              topic: data.topic || '',
              lastSaved: data.lastSaved ? new Date(data.lastSaved) : undefined
            }));
          }
        } catch (error) {
          console.error('Error loading from localStorage:', error);
        }
      }
    };

    if (config.projectId) {
      loadContent();
    }
  }, [config.projectId, config.enableLocalStorage, localStorageKey]);

  // Save to localStorage immediately
  const saveToLocalStorage = useCallback((content: string, topic: string) => {
    if (config.enableLocalStorage === false) return;

    try {
      const data = {
        content,
        topic,
        lastSaved: new Date().toISOString(),
        projectId: config.projectId
      };
      localStorage.setItem(localStorageKey, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }, [config.enableLocalStorage, config.projectId, localStorageKey]);

  // Save to server
  const saveToServer = useCallback(async (content: string, topic: string, metadata: ContentMetadata) => {
    setState(prev => ({ ...prev, isSaving: true, error: undefined }));

    try {
      const API_BASE = import.meta.env.VITE_API_BASE || "http://localhost:8001";
      
      const response = await fetch(`${API_BASE}/api/v1/seo-gpt/projects/${config.projectId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: topic,
          content_text: content,
          content_html: content, // Preserve HTML formatting
          content_length: metadata.charCount,
          word_count: metadata.wordCount,
          updated_at: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        throw new Error(`Save failed: ${response.statusText}`);
      }

      const now = new Date();
      setState(prev => ({
        ...prev,
        isDirty: false,
        isSaving: false,
        lastSaved: now,
        error: undefined
      }));

      // Clear localStorage backup after successful server save
      if (config.enableLocalStorage !== false) {
        localStorage.removeItem(localStorageKey);
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown save error';
      setState(prev => ({
        ...prev,
        isSaving: false,
        error: errorMessage
      }));
      return false;
    }
  }, [config.projectId, config.enableLocalStorage, localStorageKey]);

  // Update content
  const updateContent = useCallback((newContent: string) => {
    setState(prev => ({
      ...prev,
      content: newContent,
      isDirty: true
    }));

    // Immediate localStorage backup
    saveToLocalStorage(newContent, state.topic);
  }, [saveToLocalStorage, state.topic]);

  // Update topic
  const updateTopic = useCallback((newTopic: string) => {
    setState(prev => ({
      ...prev,
      topic: newTopic,
      isDirty: true
    }));

    // Immediate localStorage backup
    saveToLocalStorage(state.content, newTopic);
  }, [saveToLocalStorage, state.content]);

  // Force save
  const forceSave = useCallback(async () => {
    if (!state.isDirty) return true;

    const metadata: ContentMetadata = {
      wordCount: state.content.trim().split(/\s+/).filter(w => w.length > 0).length,
      charCount: state.content.length,
      readingTime: Math.max(1, Math.ceil(state.content.trim().split(/\s+/).length / 200)),
      lastModified: new Date()
    };

    return await saveToServer(state.content, state.topic, metadata);
  }, [state.content, state.topic, state.isDirty, saveToServer]);

  // Auto-save with debouncing
  const autoSave = useCallback(async () => {
    if (!state.isDirty || state.isSaving) return;

    const metadata: ContentMetadata = {
      wordCount: state.content.trim().split(/\s+/).filter(w => w.length > 0).length,
      charCount: state.content.length,
      readingTime: Math.max(1, Math.ceil(state.content.trim().split(/\s+/).length / 200)),
      lastModified: new Date()
    };

    await saveToServer(state.content, state.topic, metadata);
  }, [state.content, state.topic, state.isDirty, state.isSaving, saveToServer]);

  // Clear localStorage
  const clearLocalStorage = useCallback(() => {
    if (config.enableLocalStorage !== false) {
      localStorage.removeItem(localStorageKey);
    }
  }, [config.enableLocalStorage, localStorageKey]);

  // Get content metadata
  const getMetadata = useCallback((): ContentMetadata => {
    const words = state.content.trim().split(/\s+/).filter(w => w.length > 0);
    return {
      wordCount: words.length,
      charCount: state.content.length,
      readingTime: Math.max(1, Math.ceil(words.length / 200)),
      lastModified: new Date()
    };
  }, [state.content]);

  return {
    // State
    content: state.content,
    topic: state.topic,
    lastSaved: state.lastSaved,
    isDirty: state.isDirty,
    isSaving: state.isSaving,
    error: state.error,

    // Actions
    updateContent,
    updateTopic,
    forceSave,
    autoSave,
    clearLocalStorage,

    // Utilities
    getMetadata
  };
};

export default useContentPersistence;
