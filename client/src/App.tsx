import { Switch, Route } from "wouter";
import { Suspense, lazy } from "react";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";

// Make queryClient available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).queryClient = queryClient;
}
import { Toaster } from "@/components/ui/toaster";
import { BackgroundTasksProvider } from "@/context/BackgroundTasksContext";
import { BackgroundTasksIndicator } from "@/components/BackgroundTasksIndicator";
import { ErrorBoundary } from "@/components/common/ErrorBoundary";
import { LanguageProvider } from "@/contexts/LanguageContext";
import NotFound from "@/pages/not-found";
import OldLandingPage from "@/pages/home";
import AuthPage from "@/pages/auth-page";
import LoginPage from "@/pages/login-page";
import RegisterPage from "@/pages/register-page";
import AuthCallbackPage from "@/pages/auth-callback-page";
import DashboardPage from "@/pages/dashboard-elegant";
import NewLandingPage from "@/pages/new-landing-page";
import LandingPage from "@/pages/landing-page";

// Lazy load heavy components for better performance
const PolotnoTestPage = lazy(() => import("@/pages/polotno-test-page"));
const AdCreatorEditor = lazy(() => import("@/pages/ad-creator-editor-page"));
const EmmaVisualStudioPage = lazy(() => import("@/pages/emma-visual-studio-v2"));
const EditorProfesional = lazy(() => import("@/pages/editor-profesional"));

// Safari-compatible loading fallback component
const PageLoadingFallback = ({ title = "Loading" }: { title?: string }) => (
  <div
    className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100"
    style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #faf5ff, #eff6ff, #e0e7ff)'
    }}
  >
    <div className="text-center" style={{ textAlign: 'center' }}>
      <div
        className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#3018ef] mx-auto mb-6"
        style={{
          width: '64px',
          height: '64px',
          border: '2px solid #e5e7eb',
          borderBottom: '2px solid #3018ef',
          borderRadius: '50%',
          margin: '0 auto 24px auto',
          animation: 'spin 1s linear infinite'
        }}
      ></div>
      <h2
        className="text-2xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-2"
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          background: 'linear-gradient(45deg, #3018ef, #dd3a5a)',
          WebkitBackgroundClip: 'text',
          backgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          color: 'transparent',
          marginBottom: '8px'
        }}
      >
        {title}
      </h2>
      <p
        className="text-gray-600"
        style={{ color: '#6b7280' }}
      >
        Loading Emma Studio...
      </p>
    </div>
    <style>{`
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
    `}</style>
  </div>
);
import TestPage from "@/pages/test-page";
import TestPageNew from "@/test-page";
import TestImagePage from "@/pages/test-image-page";
import SafariTestPage from "@/pages/safari-test-page";
import CalculadoraPage from "@/pages/calculadora-nueva-page";
import MarketingToolsPage from "@/pages/marketing-tools-page";
import MarketingDesignToolsPage from "@/pages/marketing-design-tools-page";
import MarketingSEOToolsPage from "@/pages/marketing-seo-tools-page";
import MarketingAudienceToolsPage from "@/pages/marketing-audience-tools-page";
import ToolPage from "@/pages/tool-page";
import AIContentHubPage from "@/pages/ai-content-hub-page";
import AIContentHubPageNew from "@/pages/ai-content-hub-page-new";
import AIContentCreatorPage from "@/pages/ai-content-creator-page-new";
import AIContentGeneratorPage from "@/pages/ai-content-generator-page";
import LangFlowDemoPage from "@/pages/langflow-demo-page";
import InstagramCopywriterPage from "@/pages/instagram-copywriter-page";
import SEOXAnalyzerPage from "@/pages/seox-analyzer-page";
import UserProfilePage from "@/pages/user-profile-page";

import VisualEditorPage from "@/pages/visual-editor";
import VideoEditorPage from "@/pages/video-editor-page";
import SimpleVideoGeneratorPage from "@/pages/simple-video-generator-page";
import ImageToVideoPage from "@/pages/image-to-video-page";
import VideoToolsHub from "@/pages/video-tools-hub";
import VisualToolsHub from "@/pages/visual-tools-hub";
import VideoStudioPage from "@/pages/video-studio-page";
import LumaLabsPage from "@/pages/luma-labs-page";
import GoogleVeoPage from "@/pages/google-veo-page";
import RunwayMLPage from "@/pages/runway-ml-page";
import TextToVideoPage from "@/pages/text-to-video-page";
import ShortsGeneratorPage from "@/pages/shorts-generator-page";
import ImageGeneratorPage from "@/pages/image-generator-page";
import ContentPlanner from "@/pages/content-planner";

import AIImageEditor from "@/pages/ai-image-editor";
import AIImageEditorSimple from "@/pages/ai-image-editor-simple";
import BackgroundRemoverPage from "@/pages/background-remover-page";
import ReplaceBackgroundPage from "@/pages/replace-background-page";
import SketchToImagePage from "@/pages/sketch-to-image-improved";
import ImageEnhancerPage from "@/pages/image-enhancer-page";
import Generate3DPage from "@/pages/3d-generator-page";
import GenerationStatusTestPage from "@/pages/generation-status-test-page";
import { LangChainFormPage } from "@/pages/langchain-form-page";
import AgentTeamPage from "@/pages/agent-team-page";
import HumanServicesPageNew from "@/pages/human-services-page-new";
import MarwickLanding from "@/pages/marwick-landing";
import MarwickLandingV2 from "@/pages/marwick-landing-v2";
import EmmaTeamV2 from "@/pages/emma-team-v2";
import { AuthProvider } from "@/hooks/use-auth";
import VideoGenerator from "@/pages/video-generator";
import TestAgentsPage from "@/pages/test-agents-page";
import ProfesionalesIA from "@/pages/profesionales-ia";
import SolucionesNegocio from "@/pages/soluciones-negocio";
import ConversationSimulatorPage from "@/pages/conversation-simulator-page";
import EmmaAgenticSeekPage from "@/pages/emma-agenticseek-page";
import StyleReferencePage from "@/pages/style-reference-page";
import StyleTransferPage from "@/pages/style-transfer-page";
import MobileHeaderTest from "@/pages/mobile-header-test";
import EmmaNavBarTest from "@/pages/emma-navbar-test";

import ImageEraseToolPage from "@/pages/image-erase-tool-page";
import LogoGeneratorPage from "@/pages/logo-generator-page";
import InfographicCreatorPage from "@/pages/infographic-creator-page";
import PosterCreatorPage from "@/pages/poster-creator-page";
import MemeCreatorPage from "@/pages/meme-creator-page";
import AdCreatorPage from "@/pages/ad-creator-page";
import AdTypeSelector from "@/pages/ad-type-selector-page";
import FreeGenerationPage from "@/pages/free-generation-page";
import MockupGeneratorPage from "@/pages/mockup-generator-page";
import MarcaPage from "@/pages/marca-page";
import CrearMarcaPage from "@/pages/crear-marca-page";
import VibeMarketingPage from "@/pages/vibe-marketing-page";
import MoodBoardPage from "@/pages/mood-board-page";
import MoodBoardEditorPage from "@/pages/mood-board-editor-page";
import TestMoodboardIntegration from "@/pages/test-moodboard-integration";
import AdsCentralPage from "@/pages/ads-central-page";
import AdsTemplatesPage from "@/pages/ads-templates-page";
import AgentsMarketplacePage from "@/pages/agents-marketplace-page";
import AgentsCatalogPage from "@/pages/agents-catalog-page";
import InfluencerGeneratorPage from "@/pages/influencer-generator-page";
import ColorPaletteGeneratorPage from "@/pages/color-palette-generator-page";
// Individual Agent Pages
import HunterProPage from "@/pages/agents/hunter-pro";
import CalendarBotPage from "@/pages/agents/calendar-bot";
import CommunityManagerAIPage from "@/pages/agents/community-manager-ai";
import LeadAgentPage from "@/pages/agents/lead-agent";
import SalesSupportPage from "@/pages/agents/sales-support";
import EmailCustomerServicePage from "@/pages/agents/email-customer-service";
import QueSonAgentesPage from "@/pages/que-son-agentes-page";

import SEOGPTOptimizerApp from "@/pages/seo-gpt-optimizer/SEOGPTOptimizerApp";
import AIWorkflowBuilderPage from "@/pages/ai-workflow-builder-page";
import GoogleMarketingAgencyPage from "@/pages/google-marketing-agency";
import ProductPlacementPage from "@/pages/product-placement-page";
import BlogPage from "@/pages/blog-page";

function Router() {
  return (
    <Switch>
      {/* Nueva Landing Page - PREDETERMINADA PARA TODOS */}
      <Route path="/" component={NewLandingPage} />
      <Route path="/auth" component={AuthPage} />
      <Route path="/landing" component={NewLandingPage} />
      <Route path="/new-landing" component={NewLandingPage} />

      {/* Landing Pages Alternativas */}
      <Route path="/old-landing" component={OldLandingPage} />
      <Route path="/landing-v1" component={LandingPage} />
      <Route path="/blog" component={BlogPage} />
      <Route path="/blog/:slug">
        {() => {
          const BlogPostLLM = lazy(() => import('./pages/blog-post-llm'));
          return (
            <Suspense fallback={<PageLoadingFallback title="Cargando artículo..." />}>
              <BlogPostLLM />
            </Suspense>
          );
        }}
      </Route>

      <Route path="/emma-agencia-digital">
        {() => {
          const EmmaAgenciaDigital = lazy(() => import('./pages/emma-agencia-digital'));
          return (
            <Suspense fallback={<PageLoadingFallback title="Cargando Emma Agencia Digital..." />}>
              <EmmaAgenciaDigital />
            </Suspense>
          );
        }}
      </Route>
      <Route path="/calculadora" component={CalculadoraPage} />
      <Route path="/dashboard" component={DashboardPage} />
      <Route path="/test-page" component={TestPage} />
      <Route path="/test-new" component={TestPageNew} />
      <Route path="/test-image" component={TestImagePage} />
      <Route path="/safari-test" component={SafariTestPage} />
      <Route
        path="/dashboard/herramientas-marketing"
        component={MarketingToolsPage}
      />
      {/* Subpáginas de Herramientas de Marketing */}
      <Route
        path="/dashboard/herramientas-marketing/diseno"
        component={MarketingDesignToolsPage}
      />
      <Route
        path="/dashboard/herramientas-marketing/seo"
        component={MarketingSEOToolsPage}
      />
      <Route
        path="/dashboard/herramientas-marketing/audiencia"
        component={MarketingAudienceToolsPage}
      />
      {/* Nueva ruta para el simulador de conversación */}
      <Route path="/dashboard/herramientas/buyer-persona-generator/simulador/:personaId">
        {(params) => <ConversationSimulatorPage personaId={params.personaId} />}
      </Route>

      {/* Rutas específicas para mood-board (deben ir antes de la ruta genérica) */}
      <Route path="/dashboard/herramientas/mood-board/editor/:boardId">
        {(params) => <MoodBoardEditorPage boardId={params.boardId} />}
      </Route>
      <Route path="/dashboard/herramientas/mood-board" component={MoodBoardPage} />

      {/* Test route for moodboard integration */}
      <Route path="/test/moodboard-integration" component={TestMoodboardIntegration} />

      {/* Test route for post editor */}
      <Route path="/test/post-editor">
        {() => {
          const TestPostEditor = lazy(() => import("./test-post-editor"));
          return (
            <Suspense fallback={<PageLoadingFallback title="Loading Post Editor Test" />}>
              <TestPostEditor />
            </Suspense>
          );
        }}
      </Route>

      {/* SEO & GPT Optimizer™ - Herramienta completa - NO SUSPENSE */}
      <Route path="/dashboard/herramientas/seo-gpt-optimizer/*">
        {() => <SEOGPTOptimizerApp />}
      </Route>

      {/* Ruta genérica para otras herramientas */}
      <Route path="/dashboard/herramientas/:toolId">
        {(params) => <ToolPage toolId={params.toolId} />}
      </Route>
      <Route path="/dashboard/perfil" component={UserProfilePage} />
      <Route path="/ai-content-hub" component={AIContentHubPageNew} />
      <Route path="/ai-content-hub/old" component={AIContentHubPage} />
      <Route path="/ai-content-hub/create" component={AIContentCreatorPage} />
      <Route
        path="/ai-content-generator/:subcategoryId?"
        component={AIContentGeneratorPage}
      />
      <Route path="/langflow-demo" component={LangFlowDemoPage} />
      <Route
        path="/dashboard/herramientas/instagram-copywriter"
        component={InstagramCopywriterPage}
      />
      <Route path="/instagram-copywriter" component={InstagramCopywriterPage} />
      <Route
        path="/dashboard/herramientas/seox-analyzer"
        component={SEOXAnalyzerPage}
      />
      <Route path="/seox-analyzer" component={SEOXAnalyzerPage} />
      {/* Emma Visual Studio - Ruta principal unificada */}
      <Route path="/emma-studio">
        {() => (
          <Suspense fallback={<PageLoadingFallback title="Loading Emma Visual Studio" />}>
            <EmmaVisualStudioPage />
          </Suspense>
        )}
      </Route>
      <Route path="/emma-visual-studio">
        {() => (
          <Suspense fallback={<PageLoadingFallback title="Loading Emma Visual Studio" />}>
            <EmmaVisualStudioPage />
          </Suspense>
        )}
      </Route>
      <Route path="/visual-editor" component={VisualEditorPage} />
      <Route path="/video-editor" component={VideoEditorPage} />
      <Route path="/video-generator" component={VideoGenerator} />
      <Route path="/video-generator-old" component={SimpleVideoGeneratorPage} />
      <Route
        path="/video-generator/simple"
        component={SimpleVideoGeneratorPage}
      />
      <Route
        path="/simple-video-generator"
        component={SimpleVideoGeneratorPage}
      />
      <Route path="/image-generator" component={ImageGeneratorPage} />
      <Route path="/movement" component={ImageToVideoPage} />
      <Route path="/image-to-video" component={ImageToVideoPage} />
      <Route path="/video-tools" component={VideoToolsHub} />
      <Route path="/video-studio" component={VideoStudioPage} />
      <Route path="/video-studio/luma-labs" component={LumaLabsPage} />
      <Route path="/video-studio/google-veo" component={GoogleVeoPage} />
      <Route path="/video-studio/runway-ml" component={RunwayMLPage} />
      <Route path="/video-studio/text-to-video" component={TextToVideoPage} />
      <Route path="/video-studio/shorts-generator" component={ShortsGeneratorPage} />
      <Route path="/video-studio/ad-creator" component={AdCreatorPage} />
      <Route path="/visual-tools" component={VisualToolsHub} />
      <Route path="/editor-profesional">
        {() => (
          <Suspense fallback={<PageLoadingFallback title="Loading Professional Editor" />}>
            <EditorProfesional />
          </Suspense>
        )}
      </Route>
      <Route path="/ai-image-editor" component={AIImageEditor} />
      <Route path="/ai-image-editor-simple" component={AIImageEditorSimple} />
      <Route path="/background-remover" component={BackgroundRemoverPage} />
      <Route path="/replace-background" component={ReplaceBackgroundPage} />
      <Route path="/sketch-to-image" component={SketchToImagePage} />
      <Route path="/image-enhancer" component={ImageEnhancerPage} />
      <Route path="/style-reference" component={StyleReferencePage} />
      <Route path="/style-transfer" component={StyleTransferPage} />
      {/* Ruta para el generador de paletas de color */}
      <Route path="/tools/color-palette-generator" component={ColorPaletteGeneratorPage} />
      {/* Ruta para el analizador SEO */}
      <Route path="/tools/seo-analyzer">
        {() => <ToolPage toolId="seo-analyzer" />}
      </Route>
      {/* Ruta para el analizador de titulares */}
      <Route path="/tools/headline-analyzer">
        {() => <ToolPage toolId="headline-analyzer" />}
      </Route>
      {/* Ruta para el analizador de complejidad visual */}
      <Route path="/tools/design-complexity-analyzer">
        {() => <ToolPage toolId="design-complexity-analyzer" />}
      </Route>
      {/* Ruta para el simulador de focus group */}
      <Route path="/tools/focus-group-simulator">
        {() => <ToolPage toolId="focus-group-simulator" />}
      </Route>
      {/* Redirect alternativo para SEO Analyzer */}
      <Route path="/seo-analyzer">
        {() => <ToolPage toolId="seo-analyzer" />}
      </Route>
      {/* Ruta para la nueva herramienta de borrar objetos */}
      <Route path="/image-erase-tool" component={ImageEraseToolPage} />
      {/* Ruta para la nueva herramienta de generación 3D */}
      <Route path="/3d-generator" component={Generate3DPage} />
      <Route path="/logo-generator" component={LogoGeneratorPage} />
      <Route path="/infographic-creator" component={InfographicCreatorPage} />
      <Route path="/poster-creator" component={PosterCreatorPage} />
      <Route path="/meme-creator" component={MemeCreatorPage} />
      <Route path="/ad-creator" component={AdCreatorPage} />
      <Route path="/ad-creator/select" component={AdTypeSelector} />
      <Route path="/ad-creator/free-generation">
        <Suspense fallback={<PageLoadingFallback title="Generación Libre" />}>
          <FreeGenerationPage />
        </Suspense>
      </Route>
      <Route path="/ad-creator/editor/:platform">
        <Suspense fallback={<PageLoadingFallback title="Loading Ad Creator" />}>
          <AdCreatorEditor />
        </Suspense>
      </Route>
      <Route path="/mockup-generator" component={MockupGeneratorPage} />
      <Route path="/generation-status" component={GenerationStatusTestPage} />
      <Route path="/content-planner" component={ContentPlanner} />
      <Route path="/formulario/:contentTypeId">
        {(params) => <LangChainFormPage contentTypeId={params.contentTypeId} />}
      </Route>
      <Route path="/agentes/:teamId">
        {(params) => <AgentTeamPage teamId={params.teamId} />}
      </Route>
      <Route path="/dashboard/servicios" component={HumanServicesPageNew} />
      <Route path="/dashboard/marca" component={MarcaPage} />
      <Route path="/dashboard/marca/crear" component={CrearMarcaPage} />
      <Route path="/dashboard/marca/:marcaId">
        {(params) => {
          const MarcaDetailPage = lazy(() => import('./pages/marca-detail-page'));
          return (
            <Suspense fallback={<PageLoadingFallback title="Cargando marca..." />}>
              <MarcaDetailPage marcaId={params.marcaId} />
            </Suspense>
          );
        }}
      </Route>
      <Route path="/dashboard/marca/:marcaId/respuestas">
        {(params) => {
          const BrandQualificationAnswersPage = lazy(() => import('./pages/brand-qualification-answers-page'));
          return (
            <Suspense fallback={<PageLoadingFallback title="Cargando respuestas..." />}>
              <BrandQualificationAnswersPage marcaId={params.marcaId} />
            </Suspense>
          );
        }}
      </Route>
      {/* NEW: Ads Central */}
      <Route path="/dashboard/ads-central" component={AdsCentralPage} />
      <Route path="/dashboard/ads-central/templates" component={AdsTemplatesPage} />
      {/* NEW: Influencer Generator */}
      <Route path="/dashboard/influencer-generator" component={InfluencerGeneratorPage} />
      {/* NEW: Agents Marketplace */}
      <Route path="/dashboard/agents-marketplace" component={AgentsMarketplacePage} />
      {/* NEW: Agents Catalog */}
      <Route path="/dashboard/agents-catalog" component={AgentsCatalogPage} />
      {/* What are AI Agents Page */}
      <Route path="/dashboard/que-son-agentes" component={QueSonAgentesPage} />
      {/* Individual Agent Pages */}
      <Route path="/dashboard/agents/hunter-pro" component={HunterProPage} />
      <Route path="/dashboard/agents/calendar-bot" component={CalendarBotPage} />
      <Route path="/dashboard/agents/community-manager-ai" component={CommunityManagerAIPage} />
      <Route path="/dashboard/agents/lead-agent" component={LeadAgentPage} />
      <Route path="/dashboard/agents/sales-support" component={SalesSupportPage} />
      <Route path="/dashboard/agents/email-customer-service" component={EmailCustomerServicePage} />
      <Route path="/marwick" component={MarwickLandingV2} />
      <Route path="/marwick/old" component={MarwickLanding} />
      <Route path="/emma-team" component={EmmaTeamV2} />
      <Route path="/test-agents" component={TestAgentsPage} />
      <Route path="/profesionales-ia" component={ProfesionalesIA} />
      <Route path="/soluciones-negocio" component={SolucionesNegocio} />
      {/* NEW: Vibe Marketing */}
      <Route path="/vibe-marketing" component={VibeMarketingPage} />
      {/* NEW: Polotno Studio Test */}
      <Route path="/polotno-test">
        {() => (
          <Suspense fallback={<PageLoadingFallback title="Loading Polotno Studio" />}>
            <PolotnoTestPage />
          </Suspense>
        )}
      </Route>
      {/* NEW: Emma AI - Advanced AI Agent System */}
      <Route path="/emma-agenticseek" component={EmmaAgenticSeekPage} />
      <Route path="/emma-ai" component={EmmaAgenticSeekPage} />
      <Route path="/dashboard/emma-ai" component={EmmaAgenticSeekPage} />

      {/* NEW: AI Workflow Builder - ComfyUI Integration */}
      <Route path="/ai-workflow-builder" component={AIWorkflowBuilderPage} />
      <Route path="/dashboard/herramientas/ai-workflow-builder" component={AIWorkflowBuilderPage} />
      <Route path="/comfy-workflows" component={AIWorkflowBuilderPage} />
      {/* Mobile Header Test Page */}
      <Route path="/mobile-header-test" component={MobileHeaderTest} />
      {/* Emma NavBar Test Page */}
      <Route path="/emma-navbar-test" component={EmmaNavBarTest} />
      {/* Mantener esta ruta para otras páginas del dashboard que aún no tienen componentes específicos */}
      <Route path="/dashboard/:path*" component={DashboardPage} />
      <Route path="/login" component={LoginPage} />
      <Route path="/register" component={RegisterPage} />
      <Route path="/auth/callback" component={AuthCallbackPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <LanguageProvider>
          <AuthProvider>
            <BackgroundTasksProvider>
              <Router />
              <div className="fixed top-4 right-4 z-50">
                <BackgroundTasksIndicator />
              </div>
              <Toaster />
            </BackgroundTasksProvider>
          </AuthProvider>
        </LanguageProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
