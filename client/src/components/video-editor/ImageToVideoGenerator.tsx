/**
 * Generador de Videos a partir de Imágenes
 * Sigue el mismo patrón que poster-creator, meme-creator y ad-creator
 */

import React, { useState, useRef, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { useBackgroundTasks } from "@/context/BackgroundTasksContext";
import {
  generateVideoFromImage,
  checkVideoStatus,
  pollVideoStatus,
  type ImageToVideoOptions,
  type VideoStatusResponse,
} from "@/services/image-to-video-service";
import {
  Download,
  Video,
  Upload,
  Wand2,
  Sparkles,
  Play,
  Settings,
  Loader2,
  Copy,
  Heart,
  RotateCcw,
  Zap,
  FileVideo,
  Camera,
  Trash2,
  Eye,
} from "lucide-react";

// Tipos para el estado de la aplicación
interface GeneratedVideo {
  id: string;
  url: string;
  imageUrl: string;
  seed: number;
  cfgScale: number;
  motionBucketId: number;
  timestamp: number;
}

interface SavedVideo {
  id: string;
  url: string;
  imageUrl: string;
  seed: number;
  cfgScale: number;
  motionBucketId: number;
  timestamp: number;
}

// Constantes
const SAVED_VIDEOS_KEY = 'emma_saved_videos';

// Configuración de parámetros según documentación oficial
const PARAMETER_LIMITS = {
  seed: { min: 0, max: 4294967294, default: 0 },
  cfgScale: { min: 0, max: 10, default: 1.8 },
  motionBucketId: { min: 1, max: 255, default: 127 },
};

// Hooks para localStorage
function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue] as const;
}

export default function ImageToVideoGenerator() {
  const { toast } = useToast();
  const { addTask, updateTask, tasks } = useBackgroundTasks();

  // Estados principales
  const [currentVideo, setCurrentVideo] = useState<GeneratedVideo | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // Estados para imagen
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // Estados para parámetros
  const [seed, setSeed] = useState<number>(PARAMETER_LIMITS.seed.default);
  const [cfgScale, setCfgScale] = useState<number>(PARAMETER_LIMITS.cfgScale.default);
  const [motionBucketId, setMotionBucketId] = useState<number>(PARAMETER_LIMITS.motionBucketId.default);

  // Estados para progreso
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStage, setGenerationStage] = useState("");
  const [progressInterval, setProgressInterval] = useState<NodeJS.Timeout | null>(null);
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);

  // Estados para favoritos
  const [savedVideos, setSavedVideos] = useLocalStorage<SavedVideo[]>(SAVED_VIDEOS_KEY, []);
  const [currentVideoSaved, setCurrentVideoSaved] = useState(false);
  const [mainTab, setMainTab] = useState<"latest" | "saved">("latest");

  // Función para validar archivos de imagen
  const validateImageFile = (file: File) => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'Tipo de archivo no válido. Use JPEG, PNG o WebP.' };
    }

    if (file.size > maxSize) {
      return { valid: false, error: 'El archivo es muy grande. Máximo 10MB.' };
    }

    return { valid: true };
  };

  // Función para manejar la imagen
  const handleImageChange = (file: File) => {
    const validation = validateImageFile(file);
    if (!validation.valid) {
      toast({
        title: "Archivo inválido",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    setSelectedImage(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    setCurrentVideo(null);
    setCurrentVideoSaved(false);
  };

  // Función para simular progreso realista
  const startProgressSimulation = () => {
    if (progressInterval) {
      clearInterval(progressInterval);
    }

    const videoConfig = {
      duration: 15000, // 15 segundos
      stages: [
        { progress: 10, stage: "Analizando imagen de entrada..." },
        { progress: 25, stage: "Inicializando Stable Video Diffusion..." },
        { progress: 45, stage: "Generando frames de video..." },
        { progress: 70, stage: "Aplicando movimiento natural..." },
        { progress: 85, stage: "Optimizando transiciones..." },
        { progress: 95, stage: "Codificando video final..." },
      ]
    };

    let currentStep = 0;
    let currentProgress = 0;

    setGenerationProgress(5);
    setGenerationStage("Preparando generación de video...");

    const interval = setInterval(() => {
      if (currentStep < videoConfig.stages.length) {
        const targetProgress = videoConfig.stages[currentStep].progress;
        const targetStage = videoConfig.stages[currentStep].stage;

        if (currentProgress < targetProgress) {
          currentProgress = Math.min(currentProgress + 1, targetProgress);
          setGenerationProgress(currentProgress);
        }

        if (currentProgress >= targetProgress) {
          setGenerationStage(targetStage);
          currentStep++;
        }
      } else {
        setGenerationProgress(95);
        setGenerationStage("Finalizando...");
      }
    }, 300);

    setProgressInterval(interval);

    setTimeout(() => {
      if (interval) {
        clearInterval(interval);
      }
    }, videoConfig.duration + 3000);
  };

  // Función para completar el progreso
  const completeProgress = () => {
    if (progressInterval) {
      clearInterval(progressInterval);
      setProgressInterval(null);
    }
    setGenerationProgress(100);
    setGenerationStage("¡Video generado con éxito!");

    setTimeout(() => {
      setGenerationProgress(0);
      setGenerationStage("");
    }, 2000);
  };

  // Función para resetear progreso en caso de error
  const resetProgress = () => {
    if (progressInterval) {
      clearInterval(progressInterval);
      setProgressInterval(null);
    }
    setGenerationProgress(0);
    setGenerationStage("");
  };

  // Observar las tareas completadas para actualizar el video generado
  useEffect(() => {
    if (currentTaskId && tasks) {
      const task = tasks.find((t) => t.id === currentTaskId);

      if (task) {
        if (task.status === "completed" && task.result?.url) {
          // Solo actualizar si no se ha establecido ya directamente
          if (!currentVideo) {
            console.log("🎬 Actualizando video desde useEffect:", task.result.url.substring(0, 50) + "...");

            const newVideo: GeneratedVideo = {
              id: Date.now().toString(),
              url: task.result.url,
              imageUrl: imagePreview!,
              seed,
              cfgScale,
              motionBucketId,
              timestamp: Date.now(),
            };

            setCurrentVideo(newVideo);
            completeProgress();
          }
          setCurrentTaskId(null);
          setIsGenerating(false);
        } else if (task.status === "error") {
          setIsGenerating(false);
          setCurrentTaskId(null);

          resetProgress();

          toast({
            title: "❌ Error en la generación",
            description: "Hubo un problema al generar el video. Intenta de nuevo.",
            variant: "destructive",
          });
        }
      }
    }
  }, [tasks, currentTaskId, currentVideo, imagePreview, seed, cfgScale, motionBucketId]);

  // Limpiar intervalo al desmontar el componente
  useEffect(() => {
    return () => {
      if (progressInterval) {
        clearInterval(progressInterval);
      }
    };
  }, [progressInterval]);

  // Verificar si el video actual está guardado
  useEffect(() => {
    if (currentVideo) {
      setCurrentVideoSaved(savedVideos.some(vid => vid.url === currentVideo.url));
    } else {
      setCurrentVideoSaved(false);
    }
  }, [currentVideo, savedVideos]);

  // Generar seed aleatorio
  const generateRandomSeed = () => {
    const randomSeed = Math.floor(Math.random() * 1000000);
    setSeed(randomSeed);
    toast({
      title: "🎲 Seed aleatorio generado",
      description: `Nuevo seed: ${randomSeed}`,
    });
  };

  // Función para limpiar imagen
  const handleClearImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setCurrentVideo(null);
    setCurrentVideoSaved(false);
  };

  // Resetear formulario
  const resetForm = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setSeed(PARAMETER_LIMITS.seed.default);
    setCfgScale(PARAMETER_LIMITS.cfgScale.default);
    setMotionBucketId(PARAMETER_LIMITS.motionBucketId.default);
    setCurrentVideo(null);
    setCurrentVideoSaved(false);

    toast({
      title: "🔄 Formulario limpiado",
      description: "Todos los campos han sido restablecidos",
    });
  };



  // Función para procesar video
  const handleGenerateVideo = async () => {
    if (!selectedImage) {
      toast({
        title: "Imagen requerida",
        description: "Debes subir una imagen para generar el video.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setCurrentVideo(null);
    setCurrentVideoSaved(false);

    try {
      // Iniciar simulación de progreso
      startProgressSimulation();

      const taskId = `video_${Date.now()}`;

      // Configurar opciones de generación
      const options: ImageToVideoOptions = {
        image: selectedImage,
        seed: seed,
        cfgScale: cfgScale,
        motionBucketId: motionBucketId,
      };

      console.log("🎬 Iniciando generación de video:", {
        imageName: selectedImage.name,
        imageSize: `${Math.round(selectedImage.size / 1024)} KB`,
        seed: seed === 0 ? "aleatorio" : seed,
        cfgScale,
        motionBucketId,
      });

      setCurrentTaskId(taskId);

      // Agregar tarea a la lista de tareas en segundo plano
      addTask({
        id: taskId,
        type: "video-generation",
        status: "processing",
        progress: 0,
        message: "Iniciando generación de video...",
        metadata: {
          seed,
          cfgScale,
          motionBucketId,
          imageName: selectedImage.name,
        },
      });

      // Enviar la imagen a la API para generar video
      const result = await generateVideoFromImage(options);

      if (!result.success) {
        throw new Error(result.error || "Error desconocido al iniciar generación");
      }

      if (!result.id) {
        throw new Error("No se recibió un ID de generación válido del servidor");
      }

      // Actualizar tarea con ID de generación
      updateTask(taskId, {
        status: "processing",
        progress: 10,
        message: "Video en cola de generación...",
        metadata: {
          ...options,
          generationId: result.id,
          imageName: selectedImage.name,
        },
      });

      // Usar polling automático para verificar el estado
      const finalResult = await pollVideoStatus(
        result.id,
        (status: VideoStatusResponse) => {
          if (status.success && status.status === "processing") {
            updateTask(taskId, {
              status: "processing",
              progress: Math.min(generationProgress + 10, 90),
              message: "Generando video con IA...",
            });
          }
        },
        60, // 60 intentos máximo (10 minutos)
        10000 // 10 segundos entre verificaciones
      );

      console.log("🔍 Resultado final del polling:", finalResult);

      if (finalResult.success && finalResult.status === "completed" && finalResult.video_url) {
        console.log("✅ Video completado exitosamente:", {
          video_url: finalResult.video_url ? finalResult.video_url.substring(0, 50) + "..." : "null",
          metadata: finalResult.metadata
        });

        // Video generado con éxito
        updateTask(taskId, {
          status: "completed",
          progress: 100,
          message: "¡Video generado exitosamente!",
          result: {
            url: finalResult.video_url,
            metadata: finalResult.metadata,
          },
        });

        // También actualizar el estado local directamente
        console.log("🎬 Actualizando estado currentVideo con:", finalResult.video_url ? finalResult.video_url.substring(0, 50) + "..." : "null");

        const newVideo: GeneratedVideo = {
          id: Date.now().toString(),
          url: finalResult.video_url,
          imageUrl: imagePreview!,
          seed,
          cfgScale,
          motionBucketId,
          timestamp: Date.now(),
        };

        setCurrentVideo(newVideo);
        setCurrentVideoSaved(false); // Reset saved status for new video
        completeProgress();

        toast({
          title: "🎉 ¡Video completado!",
          description: "Tu video ha sido generado exitosamente",
        });
      } else {
        // La generación falló
        const errorMsg = finalResult.error || "La generación del video falló";
        updateTask(taskId, {
          status: "error",
          progress: 0,
          message: errorMsg,
        });

        toast({
          title: "❌ Error en generación",
          description: errorMsg,
          variant: "destructive",
        });
      }
    } catch (err) {
      console.error("❌ Error al generar video:", err);
      const errorMessage = err instanceof Error ? err.message : "Error al generar el video";

      if (currentTaskId) {
        updateTask(currentTaskId, {
          status: "error",
          progress: 0,
          message: errorMessage,
        });
      }

      toast({
        title: "❌ Error inesperado",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para crear un objeto SavedVideo
  const createSavedVideo = (videoData: GeneratedVideo): SavedVideo => {
    return {
      id: videoData.id,
      url: videoData.url,
      imageUrl: videoData.imageUrl,
      seed: videoData.seed,
      cfgScale: videoData.cfgScale,
      motionBucketId: videoData.motionBucketId,
      timestamp: videoData.timestamp,
    };
  };

  // Función para manejar favoritos
  const handleToggleFavorite = useCallback(async () => {
    if (!currentVideo) return;

    try {
      if (currentVideoSaved) {
        // Remover de favoritos
        const updatedVideos = savedVideos.filter(
          (saved) => saved.url !== currentVideo.url
        );
        setSavedVideos(updatedVideos);
        setCurrentVideoSaved(false);

        toast({
          title: "💔 Removido de favoritos",
          description: "Video removido de tus favoritos.",
        });
      } else {
        // Agregar a favoritos
        const videoData = createSavedVideo(currentVideo);

        const newVideo = createSavedVideo(videoData);
        const updatedVideos = [newVideo, ...savedVideos].slice(0, 50); // Limitar a 50

        setSavedVideos(updatedVideos);
        setCurrentVideoSaved(true);

        toast({
          title: "❤️ ¡Guardado en favoritos!",
          description: "Video guardado exitosamente en tus favoritos.",
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);

      toast({
        title: "❌ Error",
        description: "No se pudo guardar el video. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [currentVideo, currentVideoSaved, savedVideos, setSavedVideos, toast]);

  // Función para descargar video
  const handleDownload = async () => {
    if (!currentVideo?.url) return;

    try {
      const response = await fetch(currentVideo.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `video-${Date.now()}.mp4`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Descarga iniciada",
        description: "El video se está descargando",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo descargar el video",
        variant: "destructive",
      });
    }
  };

  // Función para copiar video al portapapeles (URL)
  const handleCopyToClipboard = async () => {
    if (!currentVideo?.url) return;

    try {
      await navigator.clipboard.writeText(currentVideo.url);

      toast({
        title: "✅ ¡Copiado!",
        description: "URL del video copiada al portapapeles.",
      });
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo copiar la URL al portapapeles.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="relative bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 rounded-2xl p-8 mb-8 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-cyan-500/20"></div>
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

          <div className="relative z-10">
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                <Video className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-white">
                Movimiento sútil
              </h1>
            </div>
            <p className="text-xl text-purple-100 mb-6 max-w-3xl">
              Convierte imágenes estáticas en videos animados con movimiento realista usando IA avanzada.
            </p>
            <div className="flex flex-wrap gap-2">
              <Badge className="bg-white/20 text-white border-white/30">
                <Sparkles className="w-3 h-3 mr-1" />
                Movimiento realista
              </Badge>
              <Badge className="bg-white/20 text-white border-white/30">
                <Zap className="w-3 h-3 mr-1" />
                Resultados rápidos
              </Badge>
              <Badge className="bg-white/20 text-white border-white/30">
                <Wand2 className="w-3 h-3 mr-1" />
                Control preciso
              </Badge>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Contenido principal */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Panel de Control */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
          className="lg:col-span-1"
        >
          <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-xl">
                <Settings className="h-5 w-5 text-purple-600" />
                Panel de Control
              </CardTitle>
              <CardDescription>
                Configura tu imagen y parámetros para generar el video
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Subida de imagen */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">📁 Subir Imagen</Label>
                {imagePreview ? (
                  <div className="relative border-2 border-gray-300 rounded-lg overflow-hidden">
                    <img
                      src={imagePreview}
                      alt="Imagen seleccionada"
                      className="w-full h-48 object-cover bg-white"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="absolute top-1 right-1 h-6 w-6 p-0"
                      onClick={handleClearImage}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                ) : (
                  <div
                    className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors"
                    onClick={() => document.getElementById("image-upload")?.click()}
                  >
                    <Upload className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-sm text-gray-600 mb-1">
                      Haz clic para subir imagen
                    </p>
                    <p className="text-xs text-gray-500">
                      JPG, PNG, WebP (máx. 10MB)
                    </p>
                    <input
                      id="image-upload"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleImageChange(file);
                      }}
                    />
                  </div>
                )}
              </div>

              {/* Parámetros de configuración */}
              <div className="space-y-4">
                {/* Seed */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label className="text-sm font-medium">Semilla (Seed)</Label>
                    <Badge variant="outline" className="text-xs">
                      {seed === 0 ? "Aleatorio" : seed}
                    </Badge>
                  </div>
                  <div className="flex space-x-2">
                    <Input
                      type="number"
                      min={0}
                      max={4294967294}
                      value={seed}
                      onChange={(e) => setSeed(parseInt(e.target.value) || 0)}
                      placeholder="0 = aleatorio"
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={generateRandomSeed}
                    >
                      🎲
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500">
                    Controla la reproducibilidad del resultado
                  </p>
                </div>

                {/* Cantidad de Movimiento */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label className="text-sm font-medium">Cantidad de Movimiento</Label>
                    <Badge variant="outline" className="text-xs">
                      {motionBucketId}
                    </Badge>
                  </div>
                  <Slider
                    value={[motionBucketId]}
                    onValueChange={(value) => setMotionBucketId(value[0])}
                    min={1}
                    max={255}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Sutil (1)</span>
                    <span>Intenso (255)</span>
                  </div>
                </div>

                {/* Adherencia a la Imagen */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label className="text-sm font-medium">Adherencia a la Imagen</Label>
                    <Badge variant="outline" className="text-xs">
                      {cfgScale.toFixed(1)}
                    </Badge>
                  </div>
                  <Slider
                    value={[cfgScale]}
                    onValueChange={(value) => setCfgScale(value[0])}
                    min={0}
                    max={10}
                    step={0.1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Más libertad</span>
                    <span>Más fiel</span>
                  </div>
                  <p className="text-xs text-gray-500">
                    Controla qué tan fielmente el video sigue la imagen original
                  </p>
                </div>
              </div>

              {/* Botones de acción */}
              <div className="space-y-3">
                <Button
                  onClick={handleGenerateVideo}
                  disabled={isGenerating || !selectedImage}
                  className="w-full h-12 text-lg bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                      Generando video...
                    </>
                  ) : (
                    <>
                      <Video className="w-5 h-5 mr-2" />
                      Generar Video
                    </>
                  )}
                </Button>

                {selectedImage && (
                  <Button
                    onClick={resetForm}
                    variant="outline"
                    className="w-full"
                    size="sm"
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Limpiar Todo
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Área de Visualización */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="lg:col-span-2"
        >
          <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as "latest" | "saved")} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="latest" className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Última Generación
              </TabsTrigger>
              <TabsTrigger value="saved" className="flex items-center gap-2">
                <Heart className="h-4 w-4" />
                Guardados ({savedVideos.length})
              </TabsTrigger>
            </TabsList>

            {/* Tab: Última Generación */}
            <TabsContent value="latest" className="mt-0">
              <AnimatePresence mode="wait">
                {currentVideo ? (
                  <motion.div
                    key="result"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center gap-2">
                          <Sparkles className="w-5 h-5 text-purple-600" />
                          Video Generado
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        {/* Video resultado */}
                        <div className="relative group">
                          <div className="bg-black rounded-lg overflow-hidden">
                            <video
                              src={currentVideo.url}
                              controls
                              autoPlay
                              loop
                              className="w-full rounded-lg shadow-lg"
                              onError={(e) => {
                                console.error("Error al cargar el video:", e);
                                toast({
                                  title: "❌ Error de reproducción",
                                  description: "Error al cargar el video. Intenta descargarlo.",
                                  variant: "destructive",
                                });
                              }}
                            />
                          </div>
                        </div>

                        {/* Imagen original */}
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <h4 className="font-medium text-sm text-gray-700 mb-2">Imagen Original</h4>
                          <img
                            src={currentVideo.imageUrl}
                            alt="Imagen original"
                            className="w-full h-24 object-cover rounded-md"
                          />
                        </div>

                        {/* Configuración utilizada */}
                        <div className="grid grid-cols-3 gap-4 p-4 bg-purple-50 rounded-lg">
                          <div className="text-center">
                            <p className="text-xs text-gray-600">Seed</p>
                            <p className="font-medium text-sm">{currentVideo.seed}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-xs text-gray-600">CFG Scale</p>
                            <p className="font-medium text-sm">{currentVideo.cfgScale.toFixed(1)}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-xs text-gray-600">Movimiento</p>
                            <p className="font-medium text-sm">{currentVideo.motionBucketId}</p>
                          </div>
                        </div>

                        {/* Botones de acción */}
                        <div className="flex flex-wrap gap-3">
                          <Button
                            onClick={handleDownload}
                            className="flex-1 min-w-[120px]"
                            variant="outline"
                          >
                            <Download className="w-4 h-4 mr-2" />
                            Descargar
                          </Button>
                          <Button
                            onClick={handleCopyToClipboard}
                            className="flex-1 min-w-[120px]"
                            variant="outline"
                          >
                            <Copy className="w-4 h-4 mr-2" />
                            Copiar URL
                          </Button>
                          <Button
                            onClick={handleToggleFavorite}
                            className={`flex-1 min-w-[120px] ${
                              currentVideoSaved
                                ? "bg-red-500 hover:bg-red-600 text-white"
                                : "bg-purple-500 hover:bg-purple-600 text-white"
                            }`}
                          >
                            <Heart className={`w-4 h-4 mr-2 ${currentVideoSaved ? "fill-current" : ""}`} />
                            {currentVideoSaved ? "Quitar" : "Guardar"}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ) : isGenerating ? (
                  <motion.div
                    key="generating"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="text-center py-16"
                  >
                    <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                      <CardContent className="py-16">
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                          className="inline-block"
                        >
                          <Video className="h-16 w-16 text-purple-500 mx-auto mb-4" />
                        </motion.div>
                        <h3 className="text-xl font-semibold text-gray-800 mb-2">
                          {generationStage || "Generando video..."}
                        </h3>
                        <div className="max-w-md mx-auto">
                          <Progress value={generationProgress} className="h-3 mb-2" />
                          <p className="text-sm text-gray-600">
                            {generationProgress}% completado
                          </p>
                        </div>
                        <p className="text-sm text-gray-500 mt-4">
                          La generación puede tomar 2-5 minutos
                        </p>
                      </CardContent>
                    </Card>
                  </motion.div>
                ) : (
                  <motion.div
                    key="placeholder"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="text-center py-16"
                  >
                    <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                      <CardContent className="py-16">
                        <div className="space-y-4">
                          <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mx-auto">
                            <Video className="w-12 h-12 text-purple-500" />
                          </div>
                          <h3 className="text-xl font-semibold text-gray-700">
                            ¡Crea tu primer video!
                          </h3>
                          <p className="text-gray-500 max-w-md mx-auto">
                            Sube una imagen y configura los parámetros para generar un video animado con IA.
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )}
              </AnimatePresence>
            </TabsContent>

            {/* Tab: Guardados */}
            <TabsContent value="saved" className="mt-0">
              {savedVideos.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {savedVideos.map((savedVideo) => (
                    <motion.div
                      key={savedVideo.id}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm hover:shadow-xl transition-shadow">
                        <CardContent className="p-4">
                          <div className="space-y-4">
                            {/* Video resultado */}
                            <div className="relative group">
                              <div className="bg-black rounded-lg overflow-hidden">
                                <video
                                  src={savedVideo.url}
                                  className="w-full h-48 object-cover"
                                  muted
                                  onMouseEnter={(e) => e.currentTarget.play()}
                                  onMouseLeave={(e) => e.currentTarget.pause()}
                                />
                              </div>
                            </div>

                            {/* Información */}
                            <div className="space-y-2">
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <p className="text-xs text-gray-500">
                                    {new Date(savedVideo.timestamp).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>

                              {/* Configuración */}
                              <div className="grid grid-cols-3 gap-2 text-xs">
                                <div className="text-center p-2 bg-gray-50 rounded">
                                  <p className="text-gray-600">Seed</p>
                                  <p className="font-medium">{savedVideo.seed}</p>
                                </div>
                                <div className="text-center p-2 bg-gray-50 rounded">
                                  <p className="text-gray-600">CFG</p>
                                  <p className="font-medium">{savedVideo.cfgScale.toFixed(1)}</p>
                                </div>
                                <div className="text-center p-2 bg-gray-50 rounded">
                                  <p className="text-gray-600">Motion</p>
                                  <p className="font-medium">{savedVideo.motionBucketId}</p>
                                </div>
                              </div>
                            </div>

                            {/* Botones de acción */}
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  // Cargar el video en la vista principal
                                  setCurrentVideo({
                                    id: savedVideo.id,
                                    url: savedVideo.url,
                                    imageUrl: savedVideo.imageUrl,
                                    seed: savedVideo.seed,
                                    cfgScale: savedVideo.cfgScale,
                                    motionBucketId: savedVideo.motionBucketId,
                                    timestamp: savedVideo.timestamp,
                                  });

                                  // Cambiar a la pestaña "Última Generación"
                                  setMainTab("latest");

                                  toast({
                                    title: "🎥 Video cargado",
                                    description: "Video cargado en la vista principal.",
                                  });
                                }}
                                className="flex-1"
                              >
                                <Eye className="w-3 h-3 mr-1" />
                                Ver
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={async () => {
                                  try {
                                    const response = await fetch(savedVideo.url);
                                    const blob = await response.blob();
                                    const url = window.URL.createObjectURL(blob);
                                    const a = document.createElement("a");
                                    a.href = url;
                                    a.download = `video-${savedVideo.id}.mp4`;
                                    document.body.appendChild(a);
                                    a.click();
                                    window.URL.revokeObjectURL(url);
                                    document.body.removeChild(a);

                                    toast({
                                      title: "Descarga iniciada",
                                      description: "El video se está descargando",
                                    });
                                  } catch (error) {
                                    toast({
                                      title: "Error",
                                      description: "No se pudo descargar el video",
                                      variant: "destructive",
                                    });
                                  }
                                }}
                                className="flex-1"
                              >
                                <Download className="w-3 h-3 mr-1" />
                                Descargar
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  const updatedVideos = savedVideos.filter(
                                    (saved) => saved.id !== savedVideo.id
                                  );
                                  setSavedVideos(updatedVideos);

                                  toast({
                                    title: "🗑️ Eliminado",
                                    description: "Video eliminado de guardados.",
                                  });
                                }}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-center py-16"
                >
                  <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                    <CardContent className="py-16">
                      <div className="space-y-4">
                        <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mx-auto">
                          <Heart className="w-12 h-12 text-purple-500" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-700">
                          No hay videos guardados
                        </h3>
                        <p className="text-gray-500 max-w-md mx-auto">
                          Los videos que guardes aparecerán aquí para acceso rápido.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </div>
  );
}