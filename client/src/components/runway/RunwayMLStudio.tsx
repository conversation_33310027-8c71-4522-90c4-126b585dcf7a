import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Wand2, 
  Image as ImageIcon, 
  Video, 
  Upload, 
  Download, 
  Loader2, 
  Play,
  Settings,
  Sparkles,
  ArrowRight,
  Clock,
  Zap
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import RunwayService, {
  RunwayWorkflowRequest,
  RunwayWorkflowResponse,
  TextToImageRequest,
  ImageGenerationResponse,
  IMAGE_RATIOS,
  VIDEO_RATIOS,
  VIDEO_DURATIONS
} from '@/services/runway-service';

interface RunwayMLStudioProps {
  className?: string;
}

export default function RunwayMLStudio({ className = "" }: RunwayMLStudioProps) {
  const [activeTab, setActiveTab] = useState<'workflow' | 'image' | 'video'>('workflow');
  const [prompt, setPrompt] = useState('');
  const [videoPrompt, setVideoPrompt] = useState('Generate a video');
  const [imageRatio, setImageRatio] = useState('1280:720');
  const [videoRatio, setVideoRatio] = useState('1280:720');
  const [videoDuration, setVideoDuration] = useState(5);
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<RunwayWorkflowResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  // States for image-only generation
  const [imagePrompt, setImagePrompt] = useState('');
  const [imageOnlyRatio, setImageOnlyRatio] = useState('1280:720');
  const [imageSeed, setImageSeed] = useState<number | undefined>(undefined);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [imageResult, setImageResult] = useState<ImageGenerationResponse | null>(null);
  const [imageError, setImageError] = useState<string | null>(null);

  const handleWorkflowGeneration = async () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);
    setError(null);
    setResult(null);

    try {
      const request: RunwayWorkflowRequest = {
        prompt_text: prompt,
        image_ratio: imageRatio,
        video_prompt: videoPrompt,
        video_ratio: videoRatio,
        video_duration: videoDuration
      };

      const response = await RunwayService.runCompleteWorkflow(request);
      setResult(response);

      if (!response.success) {
        setError(response.error || 'Generation failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleImageGeneration = async () => {
    if (!imagePrompt.trim()) return;

    setIsGeneratingImage(true);
    setImageError(null);
    setImageResult(null);

    try {
      const request: TextToImageRequest = {
        prompt_text: imagePrompt,
        ratio: imageOnlyRatio,
        seed: imageSeed
      };

      const response = await RunwayService.generateImage(request);
      setImageResult(response);

      if (!response.success) {
        setImageError(response.error || 'Image generation failed');
      }
    } catch (err) {
      setImageError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsGeneratingImage(false);
    }
  };

  const downloadMedia = async (url: string, filename: string) => {
    try {
      // Fetch the image through our backend to avoid CORS issues
      const response = await fetch('/api/runway/download-media', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url, filename }),
      });

      if (!response.ok) {
        throw new Error('Failed to download media');
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a download link
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Error downloading media:', error);
      // Fallback: try direct download (may not work due to CORS)
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 ${className}`}>
      {/* Header */}
      <div className="relative rounded-2xl overflow-hidden mb-8 backdrop-blur-xl">
        <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef] via-[#4f46e5] to-[#dd3a5a] opacity-95"></div>
        <div className="relative px-8 py-16 md:py-20 md:px-12">
          <div className="max-w-5xl">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
              className="mb-8"
            >
              <motion.span
                className="inline-flex items-center bg-white/20 backdrop-blur-md text-white font-semibold px-6 py-3 rounded-full mb-6 border border-white/30"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <Sparkles className="inline-block w-5 h-5 mr-2" />
                Runway ML
              </motion.span>
              <h1 className="text-4xl md:text-5xl lg:text-7xl font-black text-white mb-6 leading-tight">
                Runway{" "}
                <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                  Studio
                </span>
                <br className="hidden md:block" />
                <span className="text-white/90 font-light">
                  Genera imágenes y videos con IA
                </span>
              </h1>
              <p className="text-white/90 text-xl md:text-2xl max-w-3xl font-light leading-relaxed">
                Crea contenido visual impresionante con la tecnología más avanzada de generación de imágenes y videos.
              </p>
            </motion.div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-12">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-8">
            <TabsTrigger value="workflow" className="flex items-center gap-2">
              <Zap className="w-4 h-4" />
              Flujo Completo
            </TabsTrigger>
            <TabsTrigger value="image" className="flex items-center gap-2">
              <ImageIcon className="w-4 h-4" />
              Solo Imagen
            </TabsTrigger>
            <TabsTrigger value="video" className="flex items-center gap-2">
              <Video className="w-4 h-4" />
              Solo Video
            </TabsTrigger>
          </TabsList>

          <TabsContent value="workflow" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wand2 className="w-5 h-5" />
                  Generación Completa: Texto → Imagen → Video
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Input Section */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Descripción de la imagen
                      </label>
                      <Textarea
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        placeholder="Describe la imagen que quieres generar..."
                        className="min-h-[100px]"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Descripción del video
                      </label>
                      <Textarea
                        value={videoPrompt}
                        onChange={(e) => setVideoPrompt(e.target.value)}
                        placeholder="Describe cómo debe moverse la imagen..."
                        className="min-h-[80px]"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Formato de imagen
                        </label>
                        <Select value={imageRatio} onValueChange={setImageRatio}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {IMAGE_RATIOS.map((ratio) => (
                              <SelectItem key={ratio.value} value={ratio.value}>
                                {ratio.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Formato de video
                        </label>
                        <Select value={videoRatio} onValueChange={setVideoRatio}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {VIDEO_RATIOS.map((ratio) => (
                              <SelectItem key={ratio.value} value={ratio.value}>
                                {ratio.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Duración del video
                      </label>
                      <Select value={videoDuration.toString()} onValueChange={(value) => setVideoDuration(parseInt(value))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {VIDEO_DURATIONS.map((duration) => (
                            <SelectItem key={duration.value} value={duration.value.toString()}>
                              {duration.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <Button
                      onClick={handleWorkflowGeneration}
                      disabled={!prompt.trim() || isGenerating}
                      className="w-full h-12 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:from-[#3018ef]/90 hover:to-[#dd3a5a]/90"
                    >
                      {isGenerating ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="w-5 h-5 animate-spin" />
                          Generando...
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Sparkles className="w-5 h-5" />
                          Generar Imagen y Video
                        </div>
                      )}
                    </Button>
                  </div>

                  {/* Results Section */}
                  <div className="space-y-4">
                    {error && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-red-600">{error}</p>
                      </div>
                    )}

                    {result && result.success && (
                      <div className="space-y-4">
                        {/* Image Result */}
                        {result.image_result?.image_url && (
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-sm flex items-center gap-2">
                                <ImageIcon className="w-4 h-4" />
                                Imagen Generada
                              </CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="relative group">
                                <img
                                  src={result.image_result.image_url}
                                  alt="Generated image"
                                  className="w-full rounded-lg shadow-lg"
                                />
                                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                                  <Button
                                    onClick={() => downloadMedia(result.image_result!.image_url!, 'runway-image.jpg')}
                                    variant="secondary"
                                    size="sm"
                                  >
                                    <Download className="w-4 h-4 mr-2" />
                                    Descargar
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        )}

                        {/* Video Result */}
                        {result.video_result?.video_url && (
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-sm flex items-center gap-2">
                                <Video className="w-4 h-4" />
                                Video Generado
                              </CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="relative group">
                                <video
                                  src={result.video_result.video_url}
                                  controls
                                  className="w-full rounded-lg shadow-lg"
                                  poster={result.image_result?.image_url}
                                />
                              </div>
                            </CardContent>
                          </Card>
                        )}
                      </div>
                    )}

                    {isGenerating && (
                      <div className="flex items-center justify-center p-8">
                        <div className="text-center">
                          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-[#3018ef]" />
                          <p className="text-gray-600">Generando contenido...</p>
                          <p className="text-sm text-gray-500 mt-2">Esto puede tomar unos minutos</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="image" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ImageIcon className="w-5 h-5" />
                  Generación de Imágenes con IA
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Input Section */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Descripción de la imagen
                      </label>
                      <Textarea
                        value={imagePrompt}
                        onChange={(e) => setImagePrompt(e.target.value)}
                        placeholder="Describe detalladamente la imagen que quieres generar..."
                        className="min-h-[120px]"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Formato de imagen
                        </label>
                        <Select value={imageOnlyRatio} onValueChange={setImageOnlyRatio}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {IMAGE_RATIOS.map((ratio) => (
                              <SelectItem key={ratio.value} value={ratio.value}>
                                {ratio.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Seed (opcional)
                        </label>
                        <input
                          type="number"
                          value={imageSeed || ''}
                          onChange={(e) => setImageSeed(e.target.value ? parseInt(e.target.value) : undefined)}
                          placeholder="Número aleatorio"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3018ef] focus:border-transparent"
                          min="0"
                          max="4294967295"
                        />
                      </div>
                    </div>

                    <Button
                      onClick={handleImageGeneration}
                      disabled={!imagePrompt.trim() || isGeneratingImage}
                      className="w-full h-12 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:from-[#3018ef]/90 hover:to-[#dd3a5a]/90"
                    >
                      {isGeneratingImage ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="w-5 h-5 animate-spin" />
                          Generando Imagen...
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Sparkles className="w-5 h-5" />
                          Generar Imagen
                        </div>
                      )}
                    </Button>
                  </div>

                  {/* Results Section */}
                  <div className="space-y-4">
                    {imageError && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-red-600">{imageError}</p>
                      </div>
                    )}

                    {imageResult && imageResult.success && imageResult.image_url && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm flex items-center gap-2">
                            <ImageIcon className="w-4 h-4" />
                            Imagen Generada
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="relative group">
                            <img
                              src={imageResult.image_url}
                              alt="Generated image"
                              className="w-full rounded-lg shadow-lg"
                            />
                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                              <Button
                                onClick={() => downloadMedia(imageResult.image_url!, 'runway-image.jpg')}
                                variant="secondary"
                                size="sm"
                              >
                                <Download className="w-4 h-4 mr-2" />
                                Descargar
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {isGeneratingImage && (
                      <div className="flex items-center justify-center p-8">
                        <div className="text-center">
                          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-[#3018ef]" />
                          <p className="text-gray-600">Generando imagen...</p>
                          <p className="text-sm text-gray-500 mt-2">Esto puede tomar unos momentos</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="video">
            <Card>
              <CardHeader>
                <CardTitle>Generación de Videos</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Funcionalidad de solo video próximamente...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
