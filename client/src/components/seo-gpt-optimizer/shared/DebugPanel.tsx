/**
 * Debug Panel - Shows debug information on screen
 */

import React, { useState } from 'react';
import { Bug, X } from 'lucide-react';

interface DebugPanelProps {
  data: Record<string, any>;
  title?: string;
}

const DebugPanel: React.FC<DebugPanelProps> = ({ data, title = 'Debug Info' }) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 p-2 bg-red-500 text-white rounded-full shadow-lg hover:bg-red-600 z-50"
        title="Show Debug Panel"
      >
        <Bug size={20} />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/90 text-white rounded-lg shadow-xl z-50 max-w-md">
      <div className="flex items-center justify-between p-3 border-b border-gray-600">
        <div className="flex items-center gap-2">
          <Bug size={16} className="text-red-400" />
          <span className="font-semibold text-sm">{title}</span>
        </div>
        <div className="flex items-center gap-1">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 hover:bg-gray-700 rounded text-xs"
          >
            {isExpanded ? '−' : '+'}
          </button>
          <button
            onClick={() => setIsVisible(false)}
            className="p-1 hover:bg-gray-700 rounded"
          >
            <X size={14} />
          </button>
        </div>
      </div>
      
      {isExpanded && (
        <div className="p-3 max-h-96 overflow-auto">
          <pre className="text-xs whitespace-pre-wrap">
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      )}
      
      {!isExpanded && (
        <div className="p-3">
          <div className="text-xs space-y-1">
            {Object.entries(data).slice(0, 5).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="text-gray-300">{key}:</span>
                <span className="text-green-400 ml-2 truncate max-w-32">
                  {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                </span>
              </div>
            ))}
            {Object.keys(data).length > 5 && (
              <div className="text-gray-500 text-center">
                +{Object.keys(data).length - 5} more...
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default DebugPanel;
