/**
 * Input Styles - Improved visibility for all form inputs
 * Ensures text is clearly visible and accessible
 */

/* Base input styles for better visibility */
.blog-planner-input {
  color: #111827 !important;
  background-color: #ffffff !important;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5;
}

.blog-planner-input::placeholder {
  color: #9ca3af !important;
  opacity: 1;
}

.blog-planner-input:focus {
  color: #111827 !important;
  background-color: #ffffff !important;
  border-color: #3018ef !important;
  outline: none;
  box-shadow: 0 0 0 3px rgba(48, 24, 239, 0.1);
}

/* Textarea specific styles */
.blog-planner-textarea {
  color: #111827 !important;
  background-color: #ffffff !important;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.6;
  resize: none;
}

.blog-planner-textarea::placeholder {
  color: #9ca3af !important;
  opacity: 1;
  font-weight: 400;
}

.blog-planner-textarea:focus {
  color: #111827 !important;
  background-color: #ffffff !important;
  border-color: #3018ef !important;
  outline: none;
  box-shadow: 0 0 0 3px rgba(48, 24, 239, 0.1);
}

/* Select dropdown styles */
.blog-planner-select {
  color: #111827 !important;
  background-color: #ffffff !important;
  font-weight: 500;
  font-size: 14px;
}

.blog-planner-select:focus {
  color: #111827 !important;
  background-color: #ffffff !important;
  border-color: #3018ef !important;
  outline: none;
  box-shadow: 0 0 0 2px rgba(48, 24, 239, 0.1);
}

.blog-planner-select option {
  color: #111827 !important;
  background-color: #ffffff !important;
  font-weight: 500;
}

/* Keyword tag input styles */
.keyword-input {
  color: #111827 !important;
  background-color: #ffffff !important;
  font-weight: 500;
  font-size: 15px;
}

.keyword-input::placeholder {
  color: #9ca3af !important;
  opacity: 1;
  font-weight: 400;
}

.keyword-input:focus {
  color: #111827 !important;
  background-color: #ffffff !important;
  border-color: #3018ef !important;
  outline: none;
  box-shadow: 0 0 0 3px rgba(48, 24, 239, 0.1);
}

/* Dark mode compatibility */
@media (prefers-color-scheme: dark) {
  .blog-planner-input,
  .blog-planner-textarea,
  .blog-planner-select,
  .keyword-input {
    color: #111827 !important;
    background-color: #ffffff !important;
  }
  
  .blog-planner-input::placeholder,
  .blog-planner-textarea::placeholder,
  .keyword-input::placeholder {
    color: #9ca3af !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .blog-planner-input,
  .blog-planner-textarea,
  .blog-planner-select,
  .keyword-input {
    color: #000000 !important;
    background-color: #ffffff !important;
    border-color: #000000 !important;
    border-width: 2px;
  }
  
  .blog-planner-input::placeholder,
  .blog-planner-textarea::placeholder,
  .keyword-input::placeholder {
    color: #666666 !important;
  }
}

/* Focus states for accessibility */
.blog-planner-input:focus-visible,
.blog-planner-textarea:focus-visible,
.blog-planner-select:focus-visible,
.keyword-input:focus-visible {
  outline: 2px solid #3018ef;
  outline-offset: 2px;
}

/* Disabled state */
.blog-planner-input:disabled,
.blog-planner-textarea:disabled,
.blog-planner-select:disabled,
.keyword-input:disabled {
  color: #6b7280 !important;
  background-color: #f9fafb !important;
  opacity: 0.6;
  cursor: not-allowed;
}

/* Error state */
.blog-planner-input.error,
.blog-planner-textarea.error,
.blog-planner-select.error,
.keyword-input.error {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Success state */
.blog-planner-input.success,
.blog-planner-textarea.success,
.blog-planner-select.success,
.keyword-input.success {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Summary and text visibility */
.planning-summary {
  color: #111827 !important;
}

.planning-summary strong {
  color: #111827 !important;
  font-weight: 600 !important;
}

.planning-summary span {
  color: #374151 !important;
  font-weight: 500 !important;
}

/* Ensure all text in planning interface is visible */
.blog-planning-interface * {
  color: inherit;
}

.blog-planning-interface .text-gray-900 {
  color: #111827 !important;
}

.blog-planning-interface .text-gray-800 {
  color: #1f2937 !important;
}

.blog-planning-interface .text-gray-700 {
  color: #374151 !important;
}

.blog-planning-interface .text-gray-600 {
  color: #4b5563 !important;
}

.blog-planning-interface .text-gray-500 {
  color: #6b7280 !important;
}
