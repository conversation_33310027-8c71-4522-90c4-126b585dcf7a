/**
 * Comprehensive debugging and logging utility for keyword analysis
 * Provides detailed logging, error tracking, and performance monitoring
 */

import { DebugInfo, KeywordAnalysisError } from '../types/keywordAnalysis';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export interface LogEntry {
  level: LogLevel;
  timestamp: Date;
  component: string;
  message: string;
  data?: any;
  performance?: {
    startTime: number;
    endTime: number;
    duration: number;
  };
  stackTrace?: string;
}

class DebugLogger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000;
  private logLevel = LogLevel.DEBUG;
  private isProduction = process.env.NODE_ENV === 'production';

  /**
   * Set the minimum log level to display
   */
  setLogLevel(level: LogLevel) {
    this.logLevel = level;
  }

  /**
   * Log debug information
   */
  debug(component: string, message: string, data?: any) {
    this.log(LogLevel.DEBUG, component, message, data);
  }

  /**
   * Log general information
   */
  info(component: string, message: string, data?: any) {
    this.log(LogLevel.INFO, component, message, data);
  }

  /**
   * Log warnings
   */
  warn(component: string, message: string, data?: any) {
    this.log(LogLevel.WARN, component, message, data);
  }

  /**
   * Log errors with optional stack trace
   */
  error(component: string, message: string, error?: Error | any, data?: any) {
    const logData = {
      ...data,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error
    };

    this.log(LogLevel.ERROR, component, message, logData, error?.stack);
  }

  /**
   * Start performance timing
   */
  startTiming(component: string, operation: string): string {
    const timingId = `${component}-${operation}-${Date.now()}`;
    const startTime = performance.now();
    
    this.debug(component, `🚀 Starting ${operation}`, { 
      timingId, 
      startTime,
      operation 
    });

    // Store timing info for later retrieval
    (window as any).__debugTimings = (window as any).__debugTimings || {};
    (window as any).__debugTimings[timingId] = { startTime, component, operation };

    return timingId;
  }

  /**
   * End performance timing and log duration
   */
  endTiming(timingId: string, additionalData?: any): number {
    const endTime = performance.now();
    const timingInfo = (window as any).__debugTimings?.[timingId];

    if (!timingInfo) {
      this.warn('DebugLogger', `Timing ID not found: ${timingId}`);
      return 0;
    }

    const duration = endTime - timingInfo.startTime;
    
    this.info(timingInfo.component, `⏱️ Completed ${timingInfo.operation}`, {
      timingId,
      duration: `${duration.toFixed(2)}ms`,
      performance: {
        startTime: timingInfo.startTime,
        endTime,
        duration
      },
      ...additionalData
    });

    // Clean up timing info
    delete (window as any).__debugTimings[timingId];

    return duration;
  }

  /**
   * Log API call details
   */
  logApiCall(component: string, endpoint: string, params?: any, response?: any, error?: any) {
    if (error) {
      this.error(component, `❌ API call failed: ${endpoint}`, error, { params, response });
    } else {
      this.info(component, `✅ API call successful: ${endpoint}`, { params, response });
    }
  }

  /**
   * Log keyword analysis workflow steps
   */
  logKeywordAnalysis(step: string, data: any) {
    this.info('KeywordAnalysis', `🔍 ${step}`, data);
  }

  /**
   * Log research engine integration
   */
  logResearchEngine(action: string, data: any) {
    this.info('ResearchEngine', `🧠 ${action}`, data);
  }

  /**
   * Log user interactions
   */
  logUserAction(component: string, action: string, data?: any) {
    this.info(component, `👤 User action: ${action}`, data);
  }

  /**
   * Log cache operations
   */
  logCache(operation: 'hit' | 'miss' | 'set' | 'clear', key: string, data?: any) {
    const emoji = operation === 'hit' ? '🎯' : operation === 'miss' ? '❌' : operation === 'set' ? '💾' : '🗑️';
    this.debug('Cache', `${emoji} Cache ${operation}: ${key}`, data);
  }

  /**
   * Create a structured error object
   */
  createError(
    type: KeywordAnalysisError['type'],
    message: string,
    details?: any,
    retryable = false
  ): KeywordAnalysisError {
    return {
      type,
      message,
      details,
      timestamp: new Date(),
      retryable
    };
  }

  /**
   * Log the current state of keyword analysis
   */
  logAnalysisState(component: string, state: any) {
    this.debug(component, '📊 Analysis state update', {
      metricsCount: state.metrics?.length || 0,
      isAnalyzing: state.isAnalyzing,
      hasError: !!state.error,
      lastAnalysis: state.lastAnalysis,
      cacheHit: state.cacheHit
    });
  }

  /**
   * Export logs for debugging
   */
  exportLogs(): LogEntry[] {
    return [...this.logs];
  }

  /**
   * Clear all logs
   */
  clearLogs() {
    this.logs = [];
    this.info('DebugLogger', '🗑️ Logs cleared');
  }

  /**
   * Get logs filtered by component
   */
  getLogsByComponent(component: string): LogEntry[] {
    return this.logs.filter(log => log.component === component);
  }

  /**
   * Get recent error logs
   */
  getRecentErrors(minutes = 10): LogEntry[] {
    const cutoff = new Date(Date.now() - minutes * 60 * 1000);
    return this.logs.filter(log => 
      log.level === LogLevel.ERROR && log.timestamp > cutoff
    );
  }

  /**
   * Internal logging method
   */
  private log(
    level: LogLevel, 
    component: string, 
    message: string, 
    data?: any, 
    stackTrace?: string
  ) {
    if (level < this.logLevel) return;

    const logEntry: LogEntry = {
      level,
      timestamp: new Date(),
      component,
      message,
      data,
      stackTrace
    };

    // Add to internal log storage
    this.logs.push(logEntry);

    // Trim logs if too many
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output with appropriate styling
    const levelName = LogLevel[level];
    const timestamp = logEntry.timestamp.toISOString().substr(11, 12);
    const prefix = `[${timestamp}] [${levelName}] [${component}]`;

    switch (level) {
      case LogLevel.DEBUG:
        if (!this.isProduction) {
          console.debug(`%c${prefix} ${message}`, 'color: #888', data);
        }
        break;
      case LogLevel.INFO:
        console.info(`%c${prefix} ${message}`, 'color: #0066cc', data);
        break;
      case LogLevel.WARN:
        console.warn(`%c${prefix} ${message}`, 'color: #ff8800', data);
        break;
      case LogLevel.ERROR:
        console.error(`%c${prefix} ${message}`, 'color: #cc0000', data);
        if (stackTrace && !this.isProduction) {
          console.error('Stack trace:', stackTrace);
        }
        break;
    }
  }
}

// Create singleton instance
export const debugLogger = new DebugLogger();

// Convenience functions for common logging patterns
export const logKeywordAnalysis = (step: string, data: any) => 
  debugLogger.logKeywordAnalysis(step, data);

export const logResearchEngine = (action: string, data: any) => 
  debugLogger.logResearchEngine(action, data);

export const logUserAction = (component: string, action: string, data?: any) => 
  debugLogger.logUserAction(component, action, data);

export const logApiCall = (component: string, endpoint: string, params?: any, response?: any, error?: any) => 
  debugLogger.logApiCall(component, endpoint, params, response, error);

export const startTiming = (component: string, operation: string) => 
  debugLogger.startTiming(component, operation);

export const endTiming = (timingId: string, additionalData?: any) => 
  debugLogger.endTiming(timingId, additionalData);

export default debugLogger;
