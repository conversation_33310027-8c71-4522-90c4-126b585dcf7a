/**
 * Blog Topic Analyzer - Advanced topic research and analysis
 * Provides detailed topic metrics with content strategy recommendations
 */

import React, { useState, useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Search, Target, CheckCircle, ArrowUp, ArrowDown, Minus, AlertCircle, RefreshCw
} from 'lucide-react';
import { ConfidenceBadge } from './ConfidenceScoring';
import { useKeywordAnalysis } from './hooks/useKeywordAnalysis';
import { debugLogger, logUserAction } from './utils/debugLogger';
import { KeywordMetrics } from './types/keywordAnalysis';
import './InputStyles.css';



interface BlogTopicAnalyzerProps {
  topic: string;
  existingKeywords: string[];
  onKeywordSelect: (keyword: string) => void;
  onKeywordDeselect: (keyword: string) => void;
  selectedKeywords: string[];
  className?: string;
  skipAutoAnalysis?: boolean; // Para evitar regeneración cuando se vuelve del cache
}

/**
 * BlogTopicAnalyzer Component
 *
 * Advanced blog topic research and analysis component that leverages the existing
 * SEO GPT research engine to provide real topic metrics and content opportunities.
 *
 * Features:
 * - Real-time topic analysis using research engine + Gemini AI
 * - Smart caching to avoid redundant API calls
 * - Comprehensive error handling with fallback data
 * - Detailed debugging and performance monitoring
 * - Sorting and filtering capabilities
 *
 * @param topic - The main topic to analyze blog themes for
 * @param existingKeywords - Previously selected topics (for compatibility)
 * @param onKeywordSelect - Callback when user selects a topic
 * @param onKeywordDeselect - Callback when user deselects a topic
 * @param selectedKeywords - Currently selected topics
 * @param className - Additional CSS classes
 */
const KeywordOpportunityAnalyzer: React.FC<BlogTopicAnalyzerProps> = ({
  topic,
  existingKeywords: _existingKeywords, // Unused but kept for interface compatibility
  onKeywordSelect,
  onKeywordDeselect,
  selectedKeywords,
  className = '',
  skipAutoAnalysis = false
}) => {
  // Use the custom hook for all keyword analysis logic
  const {
    state: { metrics: keywordMetrics, isAnalyzing, error, cacheHit },
    analyzeKeywordOpportunities,
    retryAnalysis,
    clearAnalysis
  } = useKeywordAnalysis({
    language: 'es',
    maxKeywords: 15,
    cacheEnabled: true
  });

  // UI state for sorting and filtering
  const [sortBy, setSortBy] = useState<'opportunity' | 'difficulty' | 'content_opportunity'>('opportunity');
  const [filterBy, setFilterBy] = useState<'all' | 'Excelente' | 'Bueno' | 'Mejorable'>('all');



  /**
   * Handle manual analysis trigger
   */
  const handleAnalyzeClick = useCallback(async () => {
    logUserAction('KeywordOpportunityAnalyzer', 'manual_analysis_trigger', { topic });
    await analyzeKeywordOpportunities(topic);
  }, [topic, analyzeKeywordOpportunities]);

  /**
   * Handle retry after error
   */
  const handleRetryClick = useCallback(async () => {
    logUserAction('KeywordOpportunityAnalyzer', 'retry_analysis', { topic, errorType: error?.type });
    await retryAnalysis(topic);
  }, [topic, retryAnalysis, error]);



  // Sort blog topics - FORCE re-render by using state
  const [sortedKeywords, setSortedKeywords] = React.useState<KeywordMetrics[]>([]);

  // Update sorted topics when data or sort changes
  React.useEffect(() => {
    const sorted = [...keywordMetrics].sort((a, b) => {
      switch (sortBy) {
        case 'content_opportunity':
          return (b.blogMetrics?.contentOpportunity || 0) - (a.blogMetrics?.contentOpportunity || 0);
        case 'difficulty':
          return a.difficulty - b.difficulty; // Lower difficulty first
        case 'opportunity':
        default:
          return b.opportunity - a.opportunity; // Higher opportunity first
      }
    });

    console.log('🔄 Sorting Applied:', {
      sortBy,
      originalCount: keywordMetrics.length,
      sortedCount: sorted.length,
      firstThree: sorted.slice(0, 3).map(k => ({
        keyword: k.keyword.substring(0, 30) + '...',
        opportunity: k.opportunity,
        difficulty: k.difficulty,
        contentOpportunity: k.blogMetrics?.contentOpportunity
      })),
      sortingValues: sorted.slice(0, 5).map(k => {
        switch (sortBy) {
          case 'content_opportunity':
            return { keyword: k.keyword.substring(0, 20), value: k.blogMetrics?.contentOpportunity || 0 };
          case 'difficulty':
            return { keyword: k.keyword.substring(0, 20), value: k.difficulty };
          case 'opportunity':
          default:
            return { keyword: k.keyword.substring(0, 20), value: k.opportunity };
        }
      })
    });

    setSortedKeywords(sorted);
  }, [keywordMetrics, sortBy]);

  // Filter blog topics with debugging
  const filteredKeywords = sortedKeywords.filter(keyword => {
    if (filterBy === 'all') return true;
    return keyword.confidence === filterBy;
  });


  // Get intent styling
  const getIntentStyle = (intent: string) => {
    const styles = {
      'informational': 'bg-blue-100 text-blue-800 border border-blue-200',
      'commercial': 'bg-green-100 text-green-800 border border-green-200',
      'navigational': 'bg-purple-100 text-purple-800 border border-purple-200'
    };
    return styles[intent as keyof typeof styles] || styles['informational'];
  };

  // Get intent label
  const getIntentLabel = (intent: string) => {
    const labels = {
      'informational': 'Info',
      'commercial': 'Comercial',
      'navigational': 'Navegación'
    };
    return labels[intent as keyof typeof labels] || 'Info';
  };

  // Get trend icon
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <ArrowUp className="w-3 h-3 text-green-600" />;
      case 'down': return <ArrowDown className="w-3 h-3 text-red-600" />;
      case 'stable': return <Minus className="w-3 h-3 text-gray-600" />;
      default: return <Minus className="w-3 h-3 text-gray-600" />;
    }
  };

  // Auto-analyze when topic changes (debounced) - but skip if cache indicates analysis already done
  useEffect(() => {
    if (topic.trim() && !skipAutoAnalysis) {
      const timeoutId = setTimeout(() => {
        debugLogger.debug('KeywordOpportunityAnalyzer', 'Auto-triggering analysis after topic change', { topic });
        analyzeKeywordOpportunities(topic);
      }, 1000);
      return () => clearTimeout(timeoutId);
    } else if (skipAutoAnalysis) {
      // Skipping auto-analysis - using cached results
    } else {
      clearAnalysis();
    }
  }, [topic, analyzeKeywordOpportunities, clearAnalysis, skipAutoAnalysis]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <Target className="w-4 h-4 text-purple-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Análisis de Temas para Blog</h3>
            <p className="text-xs text-gray-600">Temas con mayor potencial para contenido</p>
          </div>
        </div>
        
        <button
          onClick={handleAnalyzeClick}
          disabled={isAnalyzing || !topic.trim()}
          className="px-3 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 transition-all duration-200 text-sm font-medium"
        >
          {isAnalyzing ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin inline-block mr-2" />
              Analizando...
            </>
          ) : (
            <>
              <Search className="w-4 h-4 inline-block mr-2" />
              Analizar
            </>
          )}
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="font-medium text-red-900">Error en el análisis</h4>
                <p className="text-sm text-red-700">{error.message}</p>
                {error.retryable && (
                  <p className="text-xs text-red-600 mt-1">
                    Este error puede resolverse reintentando el análisis.
                  </p>
                )}
              </div>
            </div>
            {error.retryable && (
              <button
                onClick={handleRetryClick}
                disabled={isAnalyzing}
                className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-all duration-200 text-sm font-medium flex items-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                Reintentar
              </button>
            )}
          </div>
          {cacheHit && (
            <div className="mt-2 text-xs text-red-600">
              Mostrando datos de respaldo. Los resultados pueden no estar actualizados.
            </div>
          )}
        </div>
      )}

      {/* Cache Hit Indicator */}
      {cacheHit && !error && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4 text-blue-600" />
            <span className="text-sm text-blue-800">
              Resultados obtenidos de caché (datos recientes)
            </span>
          </div>
        </div>
      )}

      {/* Controls */}
      {keywordMetrics.length > 0 && (
        <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">Ordenar por:</span>
            <select
              value={sortBy}
              onChange={(e) => {
                const newSortBy = e.target.value as any;
                console.log('🔄 Sort changed:', newSortBy);
                setSortBy(newSortBy);
              }}
              className="blog-planner-select text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value="opportunity">Oportunidad Total</option>
              <option value="content_opportunity">Oportunidad Contenido</option>
              <option value="difficulty">Dificultad (Fácil primero)</option>
            </select>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">Filtrar:</span>
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value as any)}
              className="blog-planner-select text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value="all">Todas</option>
              <option value="Excelente">Excelente</option>
              <option value="Bueno">Bueno</option>
              <option value="Mejorable">Mejorable</option>
            </select>
          </div>
        </div>
      )}

      {/* Blog Topics Grid */}
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {filteredKeywords.map((metric) => (
          <motion.div
            key={metric.keyword}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
              selectedKeywords.includes(metric.keyword)
                ? 'border-[#3018ef] bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => 
              selectedKeywords.includes(metric.keyword)
                ? onKeywordDeselect(metric.keyword)
                : onKeywordSelect(metric.keyword)
            }
          >
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium text-gray-900">{metric.keyword}</span>
              <div className="flex items-center gap-2">
                {getTrendIcon(metric.trend)}
                <ConfidenceBadge level={metric.confidence} size="sm" />
              </div>
            </div>
            
            <div className="space-y-3">
              {/* Blog Metrics Row 1 */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-800 font-medium">Dificultad Ranking:</span>
                    <div className="flex items-center gap-1">
                      <div className="w-12 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-green-400 via-yellow-400 to-red-400 rounded-full"
                          style={{ width: `${metric.difficulty}%` }}
                        />
                      </div>
                      <span className="text-xs font-bold text-gray-900">{metric.difficulty}%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-800 font-medium">Oportunidad Contenido:</span>
                    <span className="font-bold text-blue-700">{metric.blogMetrics?.contentOpportunity || 0}%</span>
                  </div>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-800 font-medium">Popularidad:</span>
                    <span className="font-bold text-purple-700">{metric.blogMetrics?.topicPopularity || 0}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-800 font-medium">Intención:</span>
                    <span className={`px-2 py-1 rounded text-xs font-bold ${getIntentStyle(metric.blogMetrics?.searchIntent || 'informational')}`}>
                      {getIntentLabel(metric.blogMetrics?.searchIntent || 'informational')}
                    </span>
                  </div>
                </div>
              </div>

              {/* Blog Strategy Row */}
              <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
                <div className="text-xs font-medium text-blue-800 mb-1">📝 Estrategia de Contenido:</div>
                <div className="text-xs text-blue-700">
                  <strong>Formato:</strong> {metric.blogMetrics?.recommendedFormat || 'Artículo estándar'}
                </div>
                {metric.blogMetrics?.questionsToAnswer && metric.blogMetrics.questionsToAnswer.length > 0 && (
                  <div className="text-xs text-blue-700 mt-1">
                    <strong>Preguntas clave:</strong> {metric.blogMetrics.questionsToAnswer.slice(0, 2).join(', ')}
                    {metric.blogMetrics.questionsToAnswer.length > 2 && '...'}
                  </div>
                )}
              </div>

              {/* Overall Opportunity Score */}
              <div className="flex items-center justify-between pt-2 border-t border-gray-200">
                <span className="text-gray-800 font-medium">Oportunidad Total:</span>
                <div className="flex items-center gap-2">
                  <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 rounded-full"
                      style={{ width: `${metric.opportunity}%` }}
                    />
                  </div>
                  <span className="text-sm font-bold text-gray-900">{metric.opportunity}%</span>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Summary */}
      {selectedKeywords.length > 0 && (
        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center gap-2 mb-2">
            <CheckCircle className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-semibold text-blue-900">
              {selectedKeywords.length} temas seleccionados
            </span>
          </div>
          <div className="text-sm font-bold text-blue-800">
            Oportunidad promedio: {Math.round(filteredKeywords
              .filter(k => selectedKeywords.includes(k.keyword))
              .reduce((sum, k) => sum + k.opportunity, 0) /
              Math.max(1, filteredKeywords.filter(k => selectedKeywords.includes(k.keyword)).length)
            )}% | Artículos a crear: {filteredKeywords.filter(k => selectedKeywords.includes(k.keyword)).length}
          </div>
        </div>
      )}
    </div>
  );
};

export default KeywordOpportunityAnalyzer;
