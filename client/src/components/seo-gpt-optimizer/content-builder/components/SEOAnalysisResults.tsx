/**
 * SEO Analysis Results Component
 * Displays SEO analysis results with scoring and suggestions
 * Extracted from SEOIntelligencePanel for better maintainability
 */

import React from 'react';
import { motion } from 'framer-motion';
import {
  CheckCircle, AlertCircle, Lightbulb, BarChart3, Award, Sparkles
} from 'lucide-react';
import { SEOAnalysis } from '../../../../services/seoIntelligenceService';
import { debugLogger } from '../utils/debugLogger';

interface SEOAnalysisResultsProps {
  analysis: SEOAnalysis | null;
  isAnalyzing: boolean;
  onAnalyze: () => void;
  onGenerateSuggestions: () => void;
  className?: string;
}

/**
 * SEOAnalysisResults Component
 * 
 * Displays comprehensive SEO analysis results including:
 * - Overall SEO score with visual indicators
 * - Detailed scoring breakdown by category
 * - Actionable suggestions for improvement
 * - Performance metrics and recommendations
 * 
 * @param analysis - SEO analysis data from the service
 * @param isAnalyzing - Whether analysis is currently in progress
 * @param onAnalyze - Callback to trigger new analysis
 * @param onGenerateSuggestions - Callback to generate contextual suggestions
 * @param className - Additional CSS classes
 */
const SEOAnalysisResults: React.FC<SEOAnalysisResultsProps> = ({
  analysis,
  isAnalyzing,
  onAnalyze,
  onGenerateSuggestions,
  className = ''
}) => {
  /**
   * Get color class based on score
   */
  const getScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  /**
   * Get background color class based on score
   */
  const getScoreBg = (score: number): string => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  /**
   * Get score description
   */
  const getScoreDescription = (score: number): string => {
    if (score >= 80) return 'Excelente';
    if (score >= 60) return 'Bueno';
    return 'Mejorable';
  };

  /**
   * Handle analysis trigger with logging
   */
  const handleAnalyze = () => {
    debugLogger.info('SEOAnalysisResults', 'Manual analysis triggered by user');
    onAnalyze();
  };

  /**
   * Handle suggestions generation with logging
   */
  const handleGenerateSuggestions = () => {
    debugLogger.info('SEOAnalysisResults', 'Suggestions generation triggered by user');
    onGenerateSuggestions();
  };

  if (!analysis && !isAnalyzing) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <BarChart3 className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Análisis SEO Disponible
        </h3>
        <p className="text-gray-600 mb-4">
          Analiza tu contenido para obtener sugerencias de optimización SEO
        </p>
        <button
          onClick={handleAnalyze}
          className="px-6 py-3 bg-blue-500 text-white rounded-xl font-semibold hover:bg-blue-600 transition-all duration-200"
        >
          Analizar Contenido
        </button>
      </div>
    );
  }

  if (isAnalyzing) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-4">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Analizando Contenido...
        </h3>
        <p className="text-gray-600">
          Evaluando SEO y generando recomendaciones
        </p>
      </div>
    );
  }

  if (!analysis) return null;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Score */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6"
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-gray-900">Puntuación SEO</h3>
          <button
            onClick={handleAnalyze}
            disabled={isAnalyzing}
            className="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 transition-all duration-200"
          >
            Reanalizar
          </button>
        </div>

        <div className="flex items-center gap-6">
          <div className={`w-20 h-20 rounded-full flex items-center justify-center ${getScoreBg(analysis.overallScore)}`}>
            <span className={`text-2xl font-bold ${getScoreColor(analysis.overallScore)}`}>
              {analysis.overallScore}
            </span>
          </div>
          
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <span className={`text-lg font-semibold ${getScoreColor(analysis.overallScore)}`}>
                {getScoreDescription(analysis.overallScore)}
              </span>
              {analysis.overallScore >= 80 && <Award className="w-5 h-5 text-yellow-500" />}
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className={`h-3 rounded-full transition-all duration-500 ${
                  analysis.overallScore >= 80 ? 'bg-green-500' :
                  analysis.overallScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${analysis.overallScore}%` }}
              />
            </div>
            
            <p className="text-sm text-gray-600 mt-2">
              {analysis.overallScore >= 80 
                ? 'Tu contenido está muy bien optimizado para SEO'
                : analysis.overallScore >= 60
                ? 'Tu contenido tiene buena optimización, pero puede mejorar'
                : 'Tu contenido necesita optimización SEO significativa'
              }
            </p>
          </div>
        </div>
      </motion.div>

      {/* Detailed Scores */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6"
      >
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Desglose Detallado</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(analysis.scores).map(([category, score]) => (
            <div key={category} className="p-4 bg-gray-50 rounded-xl">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-gray-900 capitalize">
                  {category.replace(/([A-Z])/g, ' $1').trim()}
                </span>
                <span className={`font-bold ${getScoreColor(score)}`}>
                  {score}
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    score >= 80 ? 'bg-green-500' :
                    score >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${score}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Suggestions */}
      {analysis.suggestions && analysis.suggestions.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Lightbulb className="w-5 h-5 text-yellow-500" />
              Sugerencias de Mejora
            </h4>
            
            <button
              onClick={handleGenerateSuggestions}
              disabled={isAnalyzing}
              className="px-4 py-2 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-all duration-200 flex items-center gap-2"
            >
              <Sparkles className="w-4 h-4" />
              Generar Más
            </button>
          </div>

          <div className="space-y-3">
            {analysis.suggestions.map((suggestion, index) => (
              <div key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs font-bold">{index + 1}</span>
                </div>
                <div className="flex-1">
                  <p className="text-gray-800">{suggestion}</p>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Analysis Metadata */}
      {analysis.lastAnalyzed && (
        <div className="text-center text-sm text-gray-500">
          Último análisis: {new Date(analysis.lastAnalyzed).toLocaleString('es-ES')}
        </div>
      )}
    </div>
  );
};

export default SEOAnalysisResults;
