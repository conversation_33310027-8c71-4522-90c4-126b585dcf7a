/**
 * Error Boundary for Keyword Analysis Components
 * Provides graceful error handling and recovery for the keyword analysis workflow
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Bug, ExternalLink } from 'lucide-react';
import { debugLogger } from '../utils/debugLogger';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

/**
 * KeywordAnalysisErrorBoundary
 * 
 * Catches JavaScript errors anywhere in the keyword analysis component tree,
 * logs those errors, and displays a fallback UI instead of the component tree that crashed.
 * 
 * Features:
 * - Comprehensive error logging with stack traces
 * - User-friendly error messages
 * - Recovery mechanisms (retry, reset)
 * - Debug information for development
 * - Integration with the debug logger
 */
export class KeywordAnalysisErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error with comprehensive details
    debugLogger.error('KeywordAnalysisErrorBoundary', 'Component error caught', error, {
      errorInfo,
      errorId: this.state.errorId,
      retryCount: this.retryCount,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      componentStack: errorInfo.componentStack,
      errorBoundary: 'KeywordAnalysisErrorBoundary'
    });

    // Update state with error info
    this.setState({
      errorInfo
    });

    // Call optional error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Report to external error tracking service if available
    this.reportError(error, errorInfo);
  }

  /**
   * Report error to external tracking service
   */
  private reportError(error: Error, errorInfo: ErrorInfo) {
    // In a real application, you would send this to your error tracking service
    // like Sentry, Bugsnag, or LogRocket
    if (process.env.NODE_ENV === 'production') {
      console.error('Error reported to tracking service:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        errorId: this.state.errorId
      });
    }
  }

  /**
   * Attempt to recover from the error
   */
  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      
      debugLogger.info('KeywordAnalysisErrorBoundary', `Attempting recovery (${this.retryCount}/${this.maxRetries})`, {
        errorId: this.state.errorId,
        retryCount: this.retryCount
      });

      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null
      });
    } else {
      debugLogger.warn('KeywordAnalysisErrorBoundary', 'Max retries exceeded', {
        errorId: this.state.errorId,
        maxRetries: this.maxRetries
      });
    }
  };

  /**
   * Reset the error boundary completely
   */
  private handleReset = () => {
    this.retryCount = 0;
    
    debugLogger.info('KeywordAnalysisErrorBoundary', 'Error boundary reset', {
      errorId: this.state.errorId
    });

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  /**
   * Copy error details to clipboard for debugging
   */
  private copyErrorDetails = async () => {
    const errorDetails = {
      error: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    try {
      await navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
      debugLogger.info('KeywordAnalysisErrorBoundary', 'Error details copied to clipboard');
    } catch (err) {
      debugLogger.error('KeywordAnalysisErrorBoundary', 'Failed to copy error details', err);
    }
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="p-6 bg-red-50 border-2 border-red-200 rounded-xl">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
            
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-red-900 mb-2">
                Error en el Análisis de Palabras Clave
              </h3>
              
              <p className="text-red-800 mb-4">
                Ha ocurrido un error inesperado en el sistema de análisis de palabras clave. 
                Esto puede deberse a un problema temporal con los datos o la conexión.
              </p>

              {/* Error Details (Development Only) */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="mb-4 p-3 bg-red-100 rounded-lg">
                  <h4 className="font-medium text-red-900 mb-2">Detalles del Error (Desarrollo):</h4>
                  <p className="text-sm text-red-800 font-mono break-all">
                    {this.state.error.message}
                  </p>
                  {this.state.errorId && (
                    <p className="text-xs text-red-700 mt-1">
                      ID del Error: {this.state.errorId}
                    </p>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-3">
                {this.retryCount < this.maxRetries && (
                  <button
                    onClick={this.handleRetry}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all duration-200 flex items-center gap-2"
                  >
                    <RefreshCw className="w-4 h-4" />
                    Reintentar ({this.maxRetries - this.retryCount} intentos restantes)
                  </button>
                )}

                <button
                  onClick={this.handleReset}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-all duration-200"
                >
                  Reiniciar Componente
                </button>

                {process.env.NODE_ENV === 'development' && (
                  <button
                    onClick={this.copyErrorDetails}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 flex items-center gap-2"
                  >
                    <Bug className="w-4 h-4" />
                    Copiar Detalles
                  </button>
                )}

                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-all duration-200 flex items-center gap-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  Recargar Página
                </button>
              </div>

              {/* Help Text */}
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="font-medium text-yellow-900 mb-1">¿Qué puedes hacer?</h4>
                <ul className="text-sm text-yellow-800 space-y-1">
                  <li>• Intenta reintentar la operación</li>
                  <li>• Verifica tu conexión a internet</li>
                  <li>• Recarga la página si el problema persiste</li>
                  <li>• Contacta al soporte técnico si el error continúa</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default KeywordAnalysisErrorBoundary;
