/**
 * EditorToolbar - Formatting toolbar for Google Docs-style editor
 * Contains formatting buttons, lists, links, and Emma AI integration
 */

import React, { useCallback, useState, useEffect, useRef } from 'react';
import {
  Bold, Italic, Underline, Printer, List, ListOrdered,
  Link, Undo, Redo, Wand2, Type, Heading1, Heading2, Heading3
} from 'lucide-react';


import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  FORMAT_TEXT_COMMAND,
  UNDO_COMMAND,
  REDO_COMMAND,
  $getSelection,
  $isRangeSelection,
  $createParagraphNode,
} from 'lexical';
import { $createHeadingNode, $isHeadingNode, HeadingTagType } from '@lexical/rich-text';
import {
  INSERT_UNORDERED_LIST_COMMAND,
  INSERT_ORDERED_LIST_COMMAND,
} from '@lexical/list';
import { $createLinkNode } from '@lexical/link';

interface EditorToolbarProps {
  fontFamily?: string;
  onFontFamilyChange?: (fontFamily: string) => void;
}

const EditorToolbar: React.FC<EditorToolbarProps> = ({
  fontFamily = 'Arial, sans-serif',
  onFontFamilyChange
}) => {
  const [editor] = useLexicalComposerContext();
  const [isGenerating, setIsGenerating] = useState(false);
  const [showFontDropdown, setShowFontDropdown] = useState(false);
  const [showHeadingDropdown, setShowHeadingDropdown] = useState(false);
  const [currentHeading, setCurrentHeading] = useState<string>('Normal');
  const fontDropdownRef = useRef<HTMLDivElement>(null);
  const headingDropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (fontDropdownRef.current && !fontDropdownRef.current.contains(event.target as Node)) {
        setShowFontDropdown(false);
      }
      if (headingDropdownRef.current && !headingDropdownRef.current.contains(event.target as Node)) {
        setShowHeadingDropdown(false);
      }
    };

    if (showFontDropdown || showHeadingDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showFontDropdown, showHeadingDropdown]);

  const formatText = useCallback((format: 'bold' | 'italic' | 'underline') => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
  }, [editor]);

  const insertList = useCallback((listType: 'bullet' | 'number') => {
    if (listType === 'bullet') {
      editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
    } else {
      editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
    }
  }, [editor]);

  const insertLink = useCallback(() => {
    const url = prompt('Ingresa la URL:');
    if (url) {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          const linkNode = $createLinkNode(url);
          selection.insertNodes([linkNode]);
        }
      });
    }
  }, [editor]);

  const undo = useCallback(() => {
    editor.dispatchCommand(UNDO_COMMAND, undefined);
  }, [editor]);

  const redo = useCallback(() => {
    editor.dispatchCommand(REDO_COMMAND, undefined);
  }, [editor]);

  // Font families available for selection
  const fontFamilies = [
    { name: 'Arial', value: 'Arial, sans-serif' },
    { name: 'Times New Roman', value: 'Times New Roman, serif' },
    { name: 'Helvetica', value: 'Helvetica, Arial, sans-serif' },
    { name: 'Georgia', value: 'Georgia, serif' },
    { name: 'Verdana', value: 'Verdana, sans-serif' },
    { name: 'Courier New', value: 'Courier New, monospace' },
    { name: 'Trebuchet MS', value: 'Trebuchet MS, sans-serif' },
    { name: 'Palatino', value: 'Palatino, serif' }
  ];

  const getCurrentFontName = () => {
    const currentFont = fontFamilies.find(font => font.value === fontFamily);
    return currentFont ? currentFont.name : 'Arial';
  };

  const handleFontChange = (newFontFamily: string) => {
    onFontFamilyChange?.(newFontFamily);
    setShowFontDropdown(false);
  };

  // Heading options
  const headingOptions = [
    { label: 'Normal', value: 'paragraph', icon: null },
    { label: 'Título 1', value: 'h1', icon: <Heading1 size={14} /> },
    { label: 'Título 2', value: 'h2', icon: <Heading2 size={14} /> },
    { label: 'Título 3', value: 'h3', icon: <Heading3 size={14} /> },
    { label: 'Título 4', value: 'h4', icon: <span style={{ fontSize: '12px', fontWeight: 'bold' }}>H4</span> },
    { label: 'Título 5', value: 'h5', icon: <span style={{ fontSize: '11px', fontWeight: 'bold' }}>H5</span> },
    { label: 'Título 6', value: 'h6', icon: <span style={{ fontSize: '10px', fontWeight: 'bold' }}>H6</span> }
  ];

  const formatHeading = useCallback((headingType: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        const element = anchorNode.getKey() === 'root' ? anchorNode : anchorNode.getTopLevelElementOrThrow();

        if (headingType === 'paragraph') {
          if ($isHeadingNode(element)) {
            const paragraph = $createParagraphNode();
            paragraph.append(...element.getChildren());
            element.replace(paragraph);
          }
        } else {
          const headingNode = $createHeadingNode(headingType as HeadingTagType);
          if ($isHeadingNode(element)) {
            headingNode.append(...element.getChildren());
            element.replace(headingNode);
          } else {
            headingNode.append(...element.getChildren());
            element.replace(headingNode);
          }
        }
      }
    });
    setCurrentHeading(headingOptions.find(h => h.value === headingType)?.label || 'Normal');
    setShowHeadingDropdown(false);
  }, [editor, headingOptions]);

  // Update current heading based on selection
  useEffect(() => {
    const updateHeadingState = () => {
      editor.getEditorState().read(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          const anchorNode = selection.anchor.getNode();
          const element = anchorNode.getKey() === 'root' ? anchorNode : anchorNode.getTopLevelElementOrThrow();

          if ($isHeadingNode(element)) {
            const headingType = element.getTag();
            const option = headingOptions.find(h => h.value === headingType);
            setCurrentHeading(option?.label || 'Normal');
          } else {
            setCurrentHeading('Normal');
          }
        }
      });
    };

    const removeListener = editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        updateHeadingState();
      });
    });

    return removeListener;
  }, [editor]);

  const ToolbarButton: React.FC<{
    onClick: () => void;
    title: string;
    children: React.ReactNode;
  }> = ({ onClick, title, children }) => (
    <button
      onClick={onClick}
      style={{
        width: '28px',
        height: '28px',
        border: 'none',
        borderRadius: '3px',
        background: 'transparent',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
      onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
      onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
      title={title}
    >
      {children}
    </button>
  );

  const Divider = () => (
    <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 6px' }} />
  );

  return (
    <div style={{ background: 'white', borderBottom: '1px solid #e8eaed', padding: '4px 16px' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '6px', flexWrap: 'wrap' }}>
        {/* Print Button */}
        <ToolbarButton onClick={() => window.print()} title="Imprimir">
          <Printer size={14} color="#5f6368" />
        </ToolbarButton>

        <Divider />

        {/* Font Family Selector */}
        <div ref={fontDropdownRef} style={{ position: 'relative' }}>
          <button
            onClick={() => setShowFontDropdown(!showFontDropdown)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              padding: '4px 8px',
              border: '1px solid #e8eaed',
              borderRadius: '3px',
              background: 'white',
              cursor: 'pointer',
              fontSize: '12px',
              color: '#5f6368',
              minWidth: '100px',
              justifyContent: 'space-between'
            }}
            onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
            onMouseLeave={(e) => e.currentTarget.style.background = 'white'}
            title="Seleccionar fuente"
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <Type size={14} />
              <span>{getCurrentFontName()}</span>
            </div>
            <span style={{ fontSize: '10px' }}>▼</span>
          </button>

          {showFontDropdown && (
            <div style={{
              position: 'absolute',
              top: '100%',
              left: '0',
              background: 'white',
              border: '1px solid #e8eaed',
              borderRadius: '4px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
              zIndex: 1000,
              minWidth: '180px',
              maxHeight: '200px',
              overflowY: 'auto'
            }}>
              {fontFamilies.map((font) => (
                <button
                  key={font.value}
                  onClick={() => handleFontChange(font.value)}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: 'none',
                    background: fontFamily === font.value ? '#f1f3f4' : 'transparent',
                    cursor: 'pointer',
                    fontSize: '12px',
                    color: '#202124',
                    textAlign: 'left',
                    fontFamily: font.value
                  }}
                  onMouseEnter={(e) => {
                    if (fontFamily !== font.value) {
                      e.currentTarget.style.background = '#f8f9fa';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (fontFamily !== font.value) {
                      e.currentTarget.style.background = 'transparent';
                    }
                  }}
                >
                  {font.name}
                </button>
              ))}
            </div>
          )}
        </div>

        <Divider />

        {/* Heading Selector */}
        <div ref={headingDropdownRef} style={{ position: 'relative' }}>
          <button
            onClick={() => {
              console.log('Heading dropdown clicked!', showHeadingDropdown);
              setShowHeadingDropdown(!showHeadingDropdown);
            }}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              padding: '4px 8px',
              border: '2px solid #3018ef',
              borderRadius: '3px',
              background: 'white',
              cursor: 'pointer',
              fontSize: '12px',
              color: '#5f6368',
              minWidth: '120px',
              justifyContent: 'space-between'
            }}
            onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
            onMouseLeave={(e) => e.currentTarget.style.background = 'white'}
            title="Seleccionar estilo de título"
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              {headingOptions.find(h => h.label === currentHeading)?.icon}
              <span>{currentHeading}</span>
            </div>
            <span style={{ fontSize: '10px' }}>▼</span>
          </button>

          {showHeadingDropdown && (
            <div style={{
              position: 'absolute',
              top: '100%',
              left: '0',
              background: 'white',
              border: '1px solid #e8eaed',
              borderRadius: '4px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
              zIndex: 1000,
              minWidth: '200px',
              maxHeight: '300px',
              overflowY: 'auto'
            }}>
              {headingOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => formatHeading(option.value)}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: 'none',
                    background: currentHeading === option.label ? '#f1f3f4' : 'transparent',
                    cursor: 'pointer',
                    fontSize: option.value === 'h1' ? '18px' :
                             option.value === 'h2' ? '16px' :
                             option.value === 'h3' ? '14px' :
                             option.value === 'h4' ? '13px' :
                             option.value === 'h5' ? '12px' :
                             option.value === 'h6' ? '11px' : '12px',
                    fontWeight: option.value !== 'paragraph' ? 'bold' : 'normal',
                    color: '#202124',
                    textAlign: 'left',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                  onMouseEnter={(e) => {
                    if (currentHeading !== option.label) {
                      e.currentTarget.style.background = '#f8f9fa';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (currentHeading !== option.label) {
                      e.currentTarget.style.background = 'transparent';
                    }
                  }}
                >
                  {option.icon}
                  <span>{option.label}</span>
                </button>
              ))}
            </div>
          )}
        </div>

        <Divider />

        {/* Text Formatting */}
        <ToolbarButton onClick={() => formatText('bold')} title="Negrita (Ctrl+B)">
          <Bold size={14} color="#5f6368" />
        </ToolbarButton>
        <ToolbarButton onClick={() => formatText('italic')} title="Cursiva (Ctrl+I)">
          <Italic size={14} color="#5f6368" />
        </ToolbarButton>
        <ToolbarButton onClick={() => formatText('underline')} title="Subrayado (Ctrl+U)">
          <Underline size={14} color="#5f6368" />
        </ToolbarButton>

        <Divider />

        {/* Lists */}
        <ToolbarButton onClick={() => insertList('bullet')} title="Lista con viñetas">
          <List size={14} color="#5f6368" />
        </ToolbarButton>
        <ToolbarButton onClick={() => insertList('number')} title="Lista numerada">
          <ListOrdered size={14} color="#5f6368" />
        </ToolbarButton>

        <Divider />

        {/* Link */}
        <ToolbarButton onClick={insertLink} title="Insertar enlace">
          <Link size={14} color="#5f6368" />
        </ToolbarButton>

        <Divider />



        {/* Undo/Redo */}
        <ToolbarButton onClick={undo} title="Deshacer (Ctrl+Z)">
          <Undo size={14} color="#5f6368" />
        </ToolbarButton>
        <ToolbarButton onClick={redo} title="Rehacer (Ctrl+Y)">
          <Redo size={14} color="#5f6368" />
        </ToolbarButton>

        <Divider />


      </div>
    </div>
  );
};

export default EditorToolbar;
