/**
 * Emma Content Builder - Built on Etherpad Core
 * Real-time collaborative editor with <PERSON>'s branding and features
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { AnimatePresence } from 'framer-motion';
import toast from 'react-hot-toast';

import GoogleDocsEditor, { GoogleDocsEditorRef } from './GoogleDocsEditor';
import SEOIntelligencePanel from './SEOIntelligencePanel';
import EditorFunctionalityTest from './EditorFunctionalityTest';
import './EtherpadEditor.css';

interface ContentBuilderProps {
  projectId: string;
  initialContent?: string;
  initialTopic?: string;
  onContentChange?: (content: string) => void;
  onSave?: (content: string, title: string) => Promise<void>;
  onTitleChange?: (title: string) => void;
  className?: string;
}

const ContentBuilder: React.FC<ContentBuilderProps> = ({
  projectId,
  initialContent = '',
  initialTopic = '',
  onContentChange,
  onSave,
  onTitleChange,
  className = ''
}) => {
  console.log('🔥 CONTENT BUILDER - Initializing with projectId:', projectId);

  const [content, setContent] = useState(initialContent);
  const [title, setTitle] = useState(initialTopic || 'Documento sin título');
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved'>('idle');
  const [showSidebar, setShowSidebar] = useState(true);
  const [showEditorTest, setShowEditorTest] = useState(false);

  const googleDocsEditorRef = useRef<GoogleDocsEditorRef>(null);



  // Auto-save timeout ref
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle content changes with auto-save
  const handleContentChange = useCallback((newContent: string) => {
    console.log('🔥 CONTENT BUILDER - Content changed, length:', newContent?.length);

    setContent(newContent);

    if (onContentChange) {
      onContentChange(newContent);
    }

    // Clear existing timeout
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    // Auto-save functionality - only for substantial content to prevent multiple creation
    setSaveStatus('saving');

    // Debounced auto-save - only if we have substantial content
    if (newContent.length > 50) { // Only auto-save if there's substantial content
      autoSaveTimeoutRef.current = setTimeout(async () => {
        console.log('🔥 AUTO-SAVE TRIGGERED!');
        console.log('🔥 onSave function exists:', !!onSave);
        console.log('🔥 Content length:', newContent?.length);
        console.log('🔥 Title:', title);

        if (onSave) {
          try {
            console.log('🔥 Calling onSave...');
            await onSave(newContent, title);
            console.log('🔥 onSave completed successfully');
            setSaveStatus('saved');
            // Don't show toast for auto-save to avoid spam
          } catch (error) {
            console.error('❌ Auto-save failed:', error);
            setSaveStatus('idle');
            toast.error('Error en auto-guardado', {
              duration: 3000,
              icon: '⚠️'
            });
          }
        } else {
          console.log('⚠️ No onSave function provided');
          setSaveStatus('saved');
        }
      }, 5000); // Increased to 5 seconds to reduce frequency
    } else {
      setSaveStatus('idle'); // Don't auto-save short content
    }
  }, [onContentChange, onSave, title]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  // Handle title changes
  const handleTitleChange = useCallback((newTitle: string) => {
    setTitle(newTitle);
    if (onTitleChange) {
      onTitleChange(newTitle);
    }
  }, [onTitleChange]);

  // Manual save function
  const handleManualSave = useCallback(async () => {
    if (onSave) {
      try {
        setSaveStatus('saving');
        await onSave(content, title);
        setSaveStatus('saved');
        toast.success('Guardado manualmente', {
          duration: 2000,
          icon: '💾'
        });
      } catch (error) {
        console.error('Manual save failed:', error);
        setSaveStatus('idle');
        toast.error('Error al guardar manualmente', {
          duration: 4000,
          icon: '❌'
        });
      }
    }
  }, [onSave, content, title]);

  // Debug effect to track state changes
  useEffect(() => {
    console.log('🔥 CONTENT BUILDER - State updated, content length:', content?.length);
  }, [content, title, saveStatus, showSidebar, projectId]);

  console.log('🔥 CONTENT BUILDER - Rendering, content length:', content?.length);






  return (
    <div className="h-screen flex">
      {/* Google Docs Editor - Full Width */}
      <div className="flex-1">
        <GoogleDocsEditor
          ref={googleDocsEditorRef}
          content={content}
          onChange={handleContentChange}
          projectId={projectId}
          documentTitle={title}
          onTitleChange={handleTitleChange}
          collaborators={[
            {
              id: 'emma-ai',
              name: 'Emma AI',
              color: '#3018ef'
            }
          ]}
          className="h-full"
          showSidebar={showSidebar}
          onToggleSidebar={() => setShowSidebar(!showSidebar)}
          saveStatus={saveStatus}
          onManualSave={handleManualSave}
        />
      </div>

      {/* SEO Intelligence Sidebar - Conditional */}
      {showSidebar && (
        <div className="w-80 flex-shrink-0 border-l border-gray-200 bg-white">
          <SEOIntelligencePanel
            content={content}
            onContentGenerate={handleContentChange}
            projectId={projectId}
            className="h-full"
          />
        </div>
      )}



      {/* Editor Functionality Test Modal */}
      <AnimatePresence>
        {showEditorTest && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="relative max-w-4xl w-full mx-4 max-h-[90vh] overflow-auto">
              <EditorFunctionalityTest />
              <button
                onClick={() => setShowEditorTest(false)}
                className="absolute top-4 right-4 p-2 bg-white rounded-xl shadow-lg hover:bg-gray-100 hover:scale-105 transition-all duration-200"
              >
                ✕
              </button>
            </div>
          </div>
        )}
      </AnimatePresence>

      {/* Test Suite Buttons (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 flex flex-col gap-2 z-40">
          <button
            onClick={() => setShowEditorTest(true)}
            className="p-3 bg-green-500 hover:bg-green-600 text-white rounded-xl transition-all duration-200 font-semibold"
            title="Test Editor Functionality"
          >
            📝
          </button>
        </div>
      )}
    </div>
  );
};

export default ContentBuilder;
