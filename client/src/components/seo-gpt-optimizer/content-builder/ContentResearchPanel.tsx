/**
 * Content Research Panel - Comprehensive research and competitive analysis
 * Integrates with existing SEO GPT Optimizer research capabilities
 */

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search, Globe, Users, MessageCircle, TrendingUp, Eye,
  BarChart3, Target, Lightbulb, CheckCircle, AlertCircle,
  ExternalLink, Star, Award, Zap
} from 'lucide-react';
import { seoGptAPI } from '../../../services/seo-gpt-optimizer/api';
import { ConfidenceBadge, calculateConfidenceLevel } from './ConfidenceScoring';

interface ResearchData {
  search_intent: {
    primary_intent: string;
    confidence: number;
    secondary_intents: string[];
  };
  google_results: {
    results: Array<{
      title: string;
      url: string;
      snippet: string;
      position: number;
    }>;
  };
  social_insights: {
    reddit_discussions: Array<{
      title: string;
      url: string;
      score: number;
      comments: number;
    }>;
    quora_questions: Array<{
      question: string;
      url: string;
      views: number;
    }>;
  };
  entities: string[];
  common_questions: string[];
  content_opportunities: {
    gaps: string[];
    angles: string[];
    formats: string[];
  };
}

interface ContentResearchPanelProps {
  topic: string;
  keywords: string[];
  onResearchComplete: (data: ResearchData) => void;
  className?: string;
}

const ContentResearchPanel: React.FC<ContentResearchPanelProps> = ({
  topic,
  keywords,
  onResearchComplete,
  className = ''
}) => {
  const [researchData, setResearchData] = useState<ResearchData | null>(null);
  const [isResearching, setIsResearching] = useState(false);
  const [activeTab, setActiveTab] = useState<'intent' | 'competitors' | 'social' | 'opportunities'>('intent');
  const [researchProgress, setResearchProgress] = useState(0);



  // Conduct comprehensive research
  const conductResearch = useCallback(async () => {
    if (!topic.trim()) return;

    setIsResearching(true);
    setResearchProgress(0);

    try {
      // Step 1: Search Intent Analysis
      setResearchProgress(25);
      
      const research = await seoGptAPI.conductResearch({
        topic,
        target_language: 'es',
        include_reddit: true,
        include_quora: true,
        target_country: 'ES'
      });

      setResearchProgress(50);

      // Step 2: Process and enhance research data
      const enhancedData: ResearchData = {
        search_intent: {
          primary_intent: research.search_intent?.primary_intent || 'informational',
          confidence: research.search_intent?.confidence || 0.85,
          secondary_intents: research.search_intent?.secondary_intents || ['educational', 'commercial']
        },
        google_results: {
          results: research.google_results?.results?.slice(0, 10) || []
        },
        social_insights: {
          reddit_discussions: research.social_insights?.reddit_discussions || [],
          quora_questions: research.social_insights?.quora_questions || []
        },
        entities: research.entities || [],
        common_questions: research.common_questions || [],
        content_opportunities: {
          gaps: research.content_opportunities?.gaps || [
            'Falta información práctica paso a paso',
            'No hay ejemplos reales específicos',
            'Ausencia de casos de estudio detallados'
          ],
          angles: research.content_opportunities?.angles || [
            'Enfoque para principiantes',
            'Guía avanzada profesional',
            'Comparativa de métodos'
          ],
          formats: research.content_opportunities?.formats || [
            'Tutorial paso a paso',
            'Infografía explicativa',
            'Video guía'
          ]
        }
      };

      setResearchProgress(100);
      setResearchData(enhancedData);
      onResearchComplete(enhancedData);

    } catch (error) {
      console.error('Research failed:', error);
      
      // Fallback with mock comprehensive data
      const mockData: ResearchData = {
        search_intent: {
          primary_intent: 'informational',
          confidence: 0.85,
          secondary_intents: ['educational', 'commercial']
        },
        google_results: {
          results: [
            {
              title: `Guía completa de ${topic}`,
              url: 'https://example.com/guia',
              snippet: `Aprende todo sobre ${topic} con esta guía completa...`,
              position: 1
            },
            {
              title: `${topic}: Tutorial paso a paso`,
              url: 'https://example.com/tutorial',
              snippet: `Tutorial detallado sobre ${topic} para principiantes...`,
              position: 2
            }
          ]
        },
        social_insights: {
          reddit_discussions: [
            {
              title: `¿Alguien tiene experiencia con ${topic}?`,
              url: 'https://reddit.com/r/example',
              score: 156,
              comments: 43
            }
          ],
          quora_questions: [
            {
              question: `¿Cuál es la mejor manera de ${topic}?`,
              url: 'https://quora.com/question',
              views: 12500
            }
          ]
        },
        entities: [topic, 'SEO', 'contenido', 'optimización'],
        common_questions: [
          `¿Qué es ${topic}?`,
          `¿Cómo funciona ${topic}?`,
          `¿Cuáles son los beneficios de ${topic}?`,
          `¿Cómo empezar con ${topic}?`
        ],
        content_opportunities: {
          gaps: [
            'Falta información práctica paso a paso',
            'No hay ejemplos reales específicos',
            'Ausencia de casos de estudio detallados'
          ],
          angles: [
            'Enfoque para principiantes',
            'Guía avanzada profesional',
            'Comparativa de métodos'
          ],
          formats: [
            'Tutorial paso a paso',
            'Infografía explicativa',
            'Video guía'
          ]
        }
      };

      setResearchData(mockData);
      onResearchComplete(mockData);
    } finally {
      setIsResearching(false);
      setResearchProgress(0);
    }
  }, [topic, onResearchComplete]);

  // Get intent confidence using the new scoring system
  const getIntentConfidence = (confidence: number) => {
    const score = Math.round(confidence * 100);
    const level = calculateConfidenceLevel(score);
    return { level, score };
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <Search className="w-4 h-4 text-green-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Aprender sobre el Tema</h3>
            <p className="text-xs text-gray-600">Análisis competitivo y oportunidades de contenido</p>
          </div>
        </div>
        
        <button
          onClick={conductResearch}
          disabled={isResearching || !topic.trim()}
          className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 transition-all duration-200 text-sm font-medium"
        >
          {isResearching ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin inline-block mr-2" />
              Aprendiendo... {researchProgress}%
            </>
          ) : (
            <>
              <Search className="w-4 h-4 inline-block mr-2" />
              Aprender sobre este tema
            </>
          )}
        </button>
      </div>

      {/* Progress Bar */}
      {isResearching && (
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-green-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${researchProgress}%` }}
          />
        </div>
      )}

      {/* Research Results */}
      {researchData && (
        <div className="space-y-4">
          {/* Tabs */}
          <div className="flex border-b border-gray-200">
            {[
              { id: 'intent', label: 'Intención', icon: Target },
              { id: 'competitors', label: 'Competidores', icon: BarChart3 },
              { id: 'social', label: 'Social', icon: MessageCircle },
              { id: 'opportunities', label: 'Oportunidades', icon: Lightbulb }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 px-4 py-2 text-sm font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'text-[#3018ef] border-b-2 border-[#3018ef]'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <AnimatePresence mode="wait">
            {activeTab === 'intent' && (
              <motion.div
                key="intent"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="space-y-4"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                    <h4 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
                      <Target className="w-4 h-4" />
                      Intención Principal
                    </h4>
                    <p className="text-blue-800 capitalize mb-2">{researchData.search_intent.primary_intent}</p>
                    <ConfidenceBadge
                      level={getIntentConfidence(researchData.search_intent.confidence).level}
                      size="sm"
                    />
                  </div>

                  <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                    <h4 className="font-semibold text-purple-900 mb-2">Entidades Clave</h4>
                    <div className="flex flex-wrap gap-1">
                      {researchData.entities.slice(0, 6).map((entity) => (
                        <span key={entity} className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs">
                          {entity}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-gray-50 rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-3">Preguntas Comunes</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {researchData.common_questions.slice(0, 6).map((question, index) => (
                      <div key={index} className="flex items-start gap-2 p-2 bg-white rounded border">
                        <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-xs font-semibold text-blue-600">{index + 1}</span>
                        </div>
                        <span className="text-sm text-gray-700">{question}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            )}

            {activeTab === 'competitors' && (
              <motion.div
                key="competitors"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="space-y-4"
              >
                <h4 className="font-semibold text-gray-900">Top 10 Resultados de Google</h4>
                <div className="space-y-3">
                  {researchData.google_results.results.map((result, index) => (
                    <div key={index} className="p-4 border border-gray-200 rounded-xl hover:border-gray-300 transition-all duration-200">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-xs font-semibold text-blue-600">{result.position}</span>
                          </div>
                          <h5 className="font-medium text-gray-900 text-sm">{result.title}</h5>
                        </div>
                        <a
                          href={result.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      </div>
                      <p className="text-sm text-gray-600">{result.snippet}</p>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}

            {activeTab === 'social' && (
              <motion.div
                key="social"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="space-y-4"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <MessageCircle className="w-4 h-4 text-orange-600" />
                      Discusiones Reddit
                    </h4>
                    <div className="space-y-2">
                      {researchData.social_insights.reddit_discussions.map((discussion, index) => (
                        <div key={index} className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                          <h5 className="font-medium text-orange-900 text-sm mb-1">{discussion.title}</h5>
                          <div className="flex items-center gap-4 text-xs text-orange-700">
                            <span>↑ {discussion.score}</span>
                            <span>💬 {discussion.comments}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Users className="w-4 h-4 text-red-600" />
                      Preguntas Quora
                    </h4>
                    <div className="space-y-2">
                      {researchData.social_insights.quora_questions.map((question, index) => (
                        <div key={index} className="p-3 bg-red-50 rounded-lg border border-red-200">
                          <h5 className="font-medium text-red-900 text-sm mb-1">{question.question}</h5>
                          <div className="text-xs text-red-700">
                            👁️ {question.views.toLocaleString()} vistas
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {activeTab === 'opportunities' && (
              <motion.div
                key="opportunities"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="space-y-4"
              >
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                    <h4 className="font-semibold text-yellow-900 mb-3 flex items-center gap-2">
                      <AlertCircle className="w-4 h-4" />
                      Gaps de Contenido
                    </h4>
                    <ul className="space-y-2">
                      {researchData.content_opportunities.gaps.map((gap, index) => (
                        <li key={index} className="text-sm text-yellow-800 flex items-start gap-2">
                          <span className="w-1 h-1 bg-yellow-600 rounded-full mt-2 flex-shrink-0" />
                          {gap}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                    <h4 className="font-semibold text-green-900 mb-3 flex items-center gap-2">
                      <Lightbulb className="w-4 h-4" />
                      Ángulos de Contenido
                    </h4>
                    <ul className="space-y-2">
                      {researchData.content_opportunities.angles.map((angle, index) => (
                        <li key={index} className="text-sm text-green-800 flex items-start gap-2">
                          <span className="w-1 h-1 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                          {angle}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                    <h4 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                      <Star className="w-4 h-4" />
                      Formatos Sugeridos
                    </h4>
                    <ul className="space-y-2">
                      {researchData.content_opportunities.formats.map((format, index) => (
                        <li key={index} className="text-sm text-blue-800 flex items-start gap-2">
                          <span className="w-1 h-1 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                          {format}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}
    </div>
  );
};

export default ContentResearchPanel;
