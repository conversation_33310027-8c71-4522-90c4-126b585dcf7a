/**
 * Blog Planning Interface - Professional blog generation planning
 * Collects comprehensive user inputs before content creation
 */

import React, { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Wand2, Target, Search, TrendingUp, CheckCircle, AlertCircle,
  X, Plus, Trash2, Lightbulb, BarChart3, <PERSON><PERSON>les, Award
} from 'lucide-react';
import KeywordOpportunityAnalyzer from './KeywordOpportunityAnalyzer';
import ContentResearchPanel from './ContentResearchPanel';
import { debugLogger, logUserAction } from './utils/debugLogger';
import './InputStyles.css';

interface BlogPlanningData {
  topic: string;
  keywords: string[];
  targetKeywords: string[];
  suggestedKeywords: string[];
  researchData?: any;
}

interface BlogPlanningInterfaceProps {
  projectId: string;
  onComplete: (data: BlogPlanningData) => void;
  onCancel: () => void;
  isVisible: boolean;
}



const BlogPlanningInterface: React.FC<BlogPlanningInterfaceProps> = ({
  projectId,
  onComplete,
  onCancel,
  isVisible
}) => {
  const [topic, setTopic] = useState('');
  const [manualKeywords, setManualKeywords] = useState<string[]>([]);
  const [newKeyword, setNewKeyword] = useState('');
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([]);
  const [researchData, setResearchData] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState<'topic' | 'keywords' | 'research'>('topic');

  // Cache para mantener los datos entre pasos
  const [cachedData, setCachedData] = useState({
    generatedThemes: [] as any[], // Temas generados por el análisis
    lastAnalyzedTopic: '',
    selectedKeywordsCache: [] as string[], // Temas seleccionados por el usuario
    hasAnalyzedCurrentTopic: false // Flag para saber si ya analizamos este tema
  });





  // Add manual keyword
  const addManualKeyword = useCallback(() => {
    if (newKeyword.trim() && !manualKeywords.includes(newKeyword.trim())) {
      const keyword = newKeyword.trim();
      setManualKeywords(prev => [...prev, keyword]);
      setNewKeyword('');
      logUserAction('BlogPlanningInterface', 'add_manual_keyword', { keyword, totalKeywords: manualKeywords.length + 1 });
    }
  }, [newKeyword, manualKeywords]);

  // Remove manual keyword
  const removeManualKeyword = useCallback((keyword: string) => {
    setManualKeywords(prev => prev.filter(k => k !== keyword));
    logUserAction('BlogPlanningInterface', 'remove_manual_keyword', { keyword, remainingKeywords: manualKeywords.length - 1 });
  }, [manualKeywords.length]);

  // Toggle keyword selection
  const toggleKeywordSelection = useCallback((keyword: string) => {
    const isCurrentlySelected = selectedKeywords.includes(keyword);
    const newSelectedKeywords = isCurrentlySelected
      ? selectedKeywords.filter(k => k !== keyword)
      : [...selectedKeywords, keyword];

    setSelectedKeywords(newSelectedKeywords);

    // Actualizar cache
    setCachedData(prev => ({
      ...prev,
      selectedKeywordsCache: newSelectedKeywords
    }));

    logUserAction('BlogPlanningInterface', isCurrentlySelected ? 'deselect_keyword' : 'select_keyword', {
      keyword,
      totalSelected: newSelectedKeywords.length
    });
  }, [selectedKeywords]);



  // Handle step navigation with cache restoration
  const navigateToStep = useCallback((step: 'topic' | 'keywords' | 'research') => {
    // Si volvemos al paso 2 (keywords), restaurar los temas seleccionados del cache
    if (step === 'keywords') {
      // Restaurar selecciones del usuario
      if (cachedData.selectedKeywordsCache.length > 0) {
        setSelectedKeywords(cachedData.selectedKeywordsCache);
      }

      // Marcar que ya tenemos análisis para este tema para evitar regeneración
      if (cachedData.lastAnalyzedTopic === topic && cachedData.hasAnalyzedCurrentTopic) {
        // Cache restored silently - no user-facing logs
      }
    }

    setCurrentStep(step);
    logUserAction('BlogPlanningInterface', 'navigate_to_step', {
      step,
      previousStep: currentStep,
      cacheRestored: step === 'keywords' && cachedData.selectedKeywordsCache.length > 0
    });
  }, [currentStep, cachedData, topic]);

  // Efecto para mantener sincronizado el cache con los temas seleccionados
  useEffect(() => {
    setCachedData(prev => ({
      ...prev,
      selectedKeywordsCache: selectedKeywords
    }));
  }, [selectedKeywords]);

  // Efecto para marcar cuando se completa el análisis de un tema
  useEffect(() => {
    if (currentStep === 'keywords' && topic.trim()) {
      // Marcar que hemos analizado este tema para evitar regeneración
      setCachedData(prev => ({
        ...prev,
        lastAnalyzedTopic: topic,
        hasAnalyzedCurrentTopic: true
      }));
    }
  }, [currentStep, topic]);

  // Efecto para limpiar cache cuando se cierra el modal
  useEffect(() => {
    if (!isVisible) {
      setCachedData({
        generatedThemes: [],
        lastAnalyzedTopic: '',
        selectedKeywordsCache: [],
        hasAnalyzedCurrentTopic: false
      });
      setSelectedKeywords([]);
      setManualKeywords([]);
      setTopic('');
      setCurrentStep('topic');
    }
  }, [isVisible]);

  // Efecto para resetear análisis cuando cambia el tema principal
  useEffect(() => {
    if (topic !== cachedData.lastAnalyzedTopic) {
      setCachedData(prev => ({
        ...prev,
        hasAnalyzedCurrentTopic: false,
        selectedKeywordsCache: [] // Limpiar selecciones cuando cambia el tema
      }));
      setSelectedKeywords([]);
    }
  }, [topic, cachedData.lastAnalyzedTopic]);

  // Handle completion
  const handleComplete = useCallback(() => {
    const allKeywords = [...manualKeywords, ...selectedKeywords];
    const planningData = {
      topic,
      keywords: allKeywords,
      targetKeywords: manualKeywords,
      suggestedKeywords: selectedKeywords,
      researchData
    };

    logUserAction('BlogPlanningInterface', 'complete_planning', {
      topicLength: topic.length,
      totalKeywords: allKeywords.length,
      manualKeywords: manualKeywords.length,
      suggestedKeywords: selectedKeywords.length,
      hasResearchData: !!researchData
    });

    onComplete(planningData);
  }, [topic, manualKeywords, selectedKeywords, researchData, onComplete]);



  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="blog-planning-interface bg-white rounded-2xl shadow-2xl max-w-7xl w-full max-h-[95vh] overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                  <Wand2 className="w-5 h-5" />
                </div>
                <div>
                  <h2 className="text-xl font-bold">Planificador de Blog Profesional</h2>
                  <p className="text-white text-opacity-90 text-sm">
                    Optimiza tu contenido antes de generar
                  </p>
                </div>
              </div>
              <button
                onClick={onCancel}
                className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center hover:bg-opacity-30 transition-all duration-200"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            {/* Progress Steps */}
            <div className="flex items-center gap-4 mt-6">
              {[
                { id: 'topic', label: 'Tema Principal', icon: Target },
                { id: 'keywords', label: 'Palabras Clave', icon: Search },
                { id: 'research', label: 'Aprender', icon: BarChart3 }
              ].map((step, index) => (
                <div key={step.id} className="flex items-center gap-2">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                    currentStep === step.id 
                      ? 'bg-white text-[#3018ef]' 
                      : 'bg-white bg-opacity-20 text-white'
                  }`}>
                    {index + 1}
                  </div>
                  <span className="text-sm font-medium">{step.label}</span>
                  {index < 2 && <div className="w-8 h-0.5 bg-white bg-opacity-30" />}
                </div>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(95vh-200px)]">
            {currentStep === 'topic' && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <div>
                  <label className="block text-sm font-semibold mb-3" style={{ color: '#374151', fontWeight: '600' }}>
                    ¿Cuál es el tema principal de tu blog?
                  </label>
                  <textarea
                    value={topic}
                    onChange={(e) => setTopic(e.target.value)}
                    placeholder="Ejemplo: Beneficios del ejercicio cardiovascular para la salud mental..."
                    className="blog-planner-textarea w-full p-4 border-2 border-gray-200 rounded-xl focus:border-[#3018ef] focus:outline-none"
                    rows={4}
                  />
                  <p className="text-xs mt-2" style={{ color: '#6b7280', fontWeight: '500' }}>
                    Describe tu tema con detalle. Esto nos ayudará a generar mejores sugerencias de palabras clave.
                  </p>
                </div>

                <div className="flex justify-end">
                  <button
                    onClick={() => navigateToStep('keywords')}
                    disabled={!topic.trim()}
                    className="px-6 py-3 bg-[#3018ef] text-white rounded-xl font-semibold hover:bg-[#2516d6] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    Continuar a Palabras Clave
                  </button>
                </div>
              </motion.div>
            )}

            {currentStep === 'keywords' && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                {/* Manual Keywords Section */}
                <div>
                  <label className="block text-sm font-semibold mb-3" style={{ color: '#374151', fontWeight: '600' }}>
                    Palabras Clave Objetivo (Manual)
                  </label>
                  <div className="flex gap-2 mb-3">
                    <input
                      type="text"
                      value={newKeyword}
                      onChange={(e) => setNewKeyword(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addManualKeyword()}
                      placeholder="Agregar palabra clave..."
                      className="keyword-input flex-1 p-3 border-2 border-gray-200 rounded-xl focus:border-[#3018ef] focus:outline-none"
                    />
                    <button
                      onClick={addManualKeyword}
                      className="px-4 py-3 bg-[#3018ef] text-white rounded-xl hover:bg-[#2516d6] transition-all duration-200"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>

                  {/* Manual Keywords List */}
                  <div className="flex flex-wrap gap-2">
                    {manualKeywords.map((keyword) => (
                      <div
                        key={keyword}
                        className="flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-lg border border-blue-200"
                      >
                        <span className="text-sm font-medium">{keyword}</span>
                        <button
                          onClick={() => removeManualKeyword(keyword)}
                          className="w-4 h-4 text-blue-600 hover:text-blue-800"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Advanced Keyword Opportunity Analyzer */}
                <KeywordOpportunityAnalyzer
                  topic={topic}
                  existingKeywords={manualKeywords}
                  selectedKeywords={selectedKeywords}
                  onKeywordSelect={toggleKeywordSelection}
                  onKeywordDeselect={toggleKeywordSelection}
                  skipAutoAnalysis={cachedData.hasAnalyzedCurrentTopic && cachedData.lastAnalyzedTopic === topic}
                />


                {/* Navigation */}
                <div className="flex justify-between">
                  <button
                    onClick={() => navigateToStep('topic')}
                    className="px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 transition-all duration-200"
                  >
                    Volver
                  </button>
                  <button
                    onClick={() => navigateToStep('research')}
                    disabled={manualKeywords.length === 0 && selectedKeywords.length === 0}
                    className="px-6 py-3 bg-[#3018ef] text-white rounded-xl font-semibold hover:bg-[#2516d6] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    Avanzar a Creación de Blog
                  </button>
                </div>
              </motion.div>
            )}

            {currentStep === 'research' && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >


                {/* Temas Seleccionados Preview */}
                {(manualKeywords.length > 0 || selectedKeywords.length > 0) && (
                  <div className="p-4 bg-green-50 rounded-xl border border-green-200 mb-4">
                    <h4 className="font-semibold text-green-900 mb-2">✅ Temas para tu Blog</h4>
                    <div className="space-y-2">
                      {manualKeywords.length > 0 && (
                        <div>
                          <span className="text-sm font-medium text-green-800">Temas Manuales:</span>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {manualKeywords.map((keyword) => (
                              <span key={keyword} className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                                {keyword}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                      {selectedKeywords.length > 0 && (
                        <div>
                          <span className="text-sm font-medium text-green-800">Temas Seleccionados:</span>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {selectedKeywords.map((keyword) => (
                              <span key={keyword} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                                {keyword}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                      <div className="text-sm font-bold text-green-900">
                        Total: {manualKeywords.length + selectedKeywords.length} temas para crear contenido
                      </div>
                    </div>
                  </div>
                )}

                {/* Advanced Content Research Panel */}
                <ContentResearchPanel
                  topic={topic}
                  keywords={[...manualKeywords, ...selectedKeywords]}
                  onResearchComplete={(data) => setResearchData(data)}
                />

                {/* Final Summary */}
                <div className="planning-summary p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
                  <h4 className="font-semibold text-gray-900 mb-3" style={{ color: '#111827' }}>Resumen de Planificación</h4>
                  <div className="space-y-2 text-sm text-gray-900 font-medium" style={{ color: '#111827' }}>
                    <div style={{ color: '#111827', fontWeight: '500' }}>
                      <strong style={{ color: '#111827', fontWeight: '600' }}>Tema:</strong>
                      <span className="ml-2" style={{ color: '#111827', fontWeight: '500' }}>{topic || 'No especificado'}</span>
                    </div>
                    <div style={{ color: '#111827', fontWeight: '500' }}>
                      <strong style={{ color: '#111827', fontWeight: '600' }}>Temas Manuales:</strong>
                      <span className="ml-2" style={{ color: '#111827', fontWeight: '500' }}>{manualKeywords.join(', ') || 'Ninguno'}</span>
                    </div>
                    <div style={{ color: '#111827', fontWeight: '500' }}>
                      <strong style={{ color: '#111827', fontWeight: '600' }}>Temas Seleccionados:</strong>
                      <span className="ml-2" style={{ color: '#111827', fontWeight: '500' }}>{selectedKeywords.join(', ') || 'Ninguno'}</span>
                    </div>
                    <div style={{ color: '#111827', fontWeight: '500' }}>
                      <strong style={{ color: '#111827', fontWeight: '600' }}>Total de Temas:</strong>
                      <span className="ml-2" style={{ color: '#111827', fontWeight: '700' }}>{manualKeywords.length + selectedKeywords.length}</span>
                    </div>
                  </div>
                </div>

                {/* Final Navigation */}
                <div className="flex justify-between">
                  <button
                    onClick={() => navigateToStep('keywords')}
                    className="px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 transition-all duration-200"
                  >
                    Volver
                  </button>
                  <button
                    onClick={handleComplete}
                    className="px-8 py-3 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white rounded-xl font-semibold hover:opacity-90 transition-all duration-200 flex items-center gap-2"
                  >
                    <Sparkles className="w-4 h-4" />
                    Generar Blog Profesional
                  </button>
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default BlogPlanningInterface;
