/**
 * Confidence Scoring System - Visual indicators for content quality and opportunities
 * Provides 'Excelente', 'Bueno', 'Mejorable' badges with colored styling
 */

import React from 'react';
import { CheckCircle, AlertTriangle, XCircle, Star, Award, Target } from 'lucide-react';

export type ConfidenceLevel = 'Excelente' | 'Bueno' | 'Mejorable';

interface ConfidenceScore {
  level: ConfidenceLevel;
  score: number; // 0-100
  description?: string;
}

interface ConfidenceBadgeProps {
  level: ConfidenceLevel;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

interface ConfidenceBarProps {
  score: number;
  showLabel?: boolean;
  className?: string;
}

interface ConfidenceCardProps {
  title: string;
  confidence: ConfidenceScore;
  metrics?: Array<{
    label: string;
    value: string | number;
    confidence?: ConfidenceLevel;
  }>;
  className?: string;
}

// Utility functions for confidence scoring
export const calculateConfidenceLevel = (score: number): ConfidenceLevel => {
  if (score >= 80) return 'Excelente';
  if (score >= 60) return 'Bueno';
  return 'Mejorable';
};

export const getConfidenceColor = (level: ConfidenceLevel) => {
  const colors = {
    'Excelente': {
      bg: 'bg-green-100',
      text: 'text-green-800',
      border: 'border-green-200',
      ring: 'ring-green-500',
      gradient: 'from-green-400 to-green-600'
    },
    'Bueno': {
      bg: 'bg-yellow-100',
      text: 'text-yellow-800',
      border: 'border-yellow-200',
      ring: 'ring-yellow-500',
      gradient: 'from-yellow-400 to-yellow-600'
    },
    'Mejorable': {
      bg: 'bg-red-100',
      text: 'text-red-800',
      border: 'border-red-200',
      ring: 'ring-red-500',
      gradient: 'from-red-400 to-red-600'
    }
  };
  return colors[level];
};

export const getConfidenceIcon = (level: ConfidenceLevel) => {
  const icons = {
    'Excelente': CheckCircle,
    'Bueno': AlertTriangle,
    'Mejorable': XCircle
  };
  return icons[level];
};

// Confidence Badge Component
export const ConfidenceBadge: React.FC<ConfidenceBadgeProps> = ({
  level,
  size = 'md',
  showIcon = true,
  className = ''
}) => {
  const colors = getConfidenceColor(level);
  const Icon = getConfidenceIcon(level);
  
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  return (
    <div className={`inline-flex items-center gap-1.5 rounded-full font-semibold border ${colors.bg} ${colors.text} ${colors.border} ${sizeClasses[size]} ${className}`}>
      {showIcon && <Icon className={iconSizes[size]} />}
      <span>{level}</span>
    </div>
  );
};

// Confidence Progress Bar
export const ConfidenceBar: React.FC<ConfidenceBarProps> = ({
  score,
  showLabel = true,
  className = ''
}) => {
  const level = calculateConfidenceLevel(score);
  const colors = getConfidenceColor(level);

  return (
    <div className={`space-y-2 ${className}`}>
      {showLabel && (
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Puntuación de Confianza</span>
          <ConfidenceBadge level={level} size="sm" />
        </div>
      )}
      <div className="relative">
        <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
          <div 
            className={`h-full bg-gradient-to-r ${colors.gradient} rounded-full transition-all duration-500 ease-out`}
            style={{ width: `${Math.min(score, 100)}%` }}
          />
        </div>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-xs font-semibold text-white drop-shadow-sm">
            {score}%
          </span>
        </div>
      </div>
    </div>
  );
};

// Confidence Card Component
export const ConfidenceCard: React.FC<ConfidenceCardProps> = ({
  title,
  confidence,
  metrics = [],
  className = ''
}) => {
  const colors = getConfidenceColor(confidence.level);
  const Icon = getConfidenceIcon(confidence.level);

  return (
    <div className={`p-4 rounded-xl border-2 ${colors.border} ${colors.bg} ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className={`font-semibold ${colors.text}`}>{title}</h3>
        <div className={`w-8 h-8 rounded-full bg-white flex items-center justify-center ${colors.ring} ring-2`}>
          <Icon className={`w-4 h-4 ${colors.text}`} />
        </div>
      </div>

      <ConfidenceBar score={confidence.score} showLabel={false} className="mb-3" />

      {confidence.description && (
        <p className={`text-sm ${colors.text} mb-3`}>{confidence.description}</p>
      )}

      {metrics.length > 0 && (
        <div className="space-y-2">
          <h4 className={`text-sm font-medium ${colors.text}`}>Métricas:</h4>
          <div className="grid grid-cols-1 gap-2">
            {metrics.map((metric, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-white bg-opacity-50 rounded">
                <span className="text-sm text-gray-700">{metric.label}:</span>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-900">{metric.value}</span>
                  {metric.confidence && (
                    <ConfidenceBadge level={metric.confidence} size="sm" showIcon={false} />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Confidence Grid Component for multiple scores
interface ConfidenceGridProps {
  scores: Array<{
    title: string;
    confidence: ConfidenceScore;
    icon?: React.ComponentType<{ className?: string }>;
  }>;
  className?: string;
}

export const ConfidenceGrid: React.FC<ConfidenceGridProps> = ({
  scores,
  className = ''
}) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 ${className}`}>
      {scores.map((item, index) => {
        const colors = getConfidenceColor(item.confidence.level);
        const IconComponent = item.icon || Star;

        return (
          <div key={index} className={`p-4 rounded-xl border ${colors.border} ${colors.bg}`}>
            <div className="flex items-center gap-3 mb-3">
              <div className={`w-10 h-10 rounded-lg bg-white flex items-center justify-center ${colors.ring} ring-2`}>
                <IconComponent className={`w-5 h-5 ${colors.text}`} />
              </div>
              <div>
                <h3 className={`font-semibold ${colors.text}`}>{item.title}</h3>
                <ConfidenceBadge level={item.confidence.level} size="sm" />
              </div>
            </div>
            <ConfidenceBar score={item.confidence.score} showLabel={false} />
            {item.confidence.description && (
              <p className={`text-sm ${colors.text} mt-2`}>{item.confidence.description}</p>
            )}
          </div>
        );
      })}
    </div>
  );
};

// Confidence Summary Component
interface ConfidenceSummaryProps {
  overallScore: number;
  breakdown: Array<{
    category: string;
    score: number;
    weight: number;
  }>;
  className?: string;
}

export const ConfidenceSummary: React.FC<ConfidenceSummaryProps> = ({
  overallScore,
  breakdown,
  className = ''
}) => {
  const level = calculateConfidenceLevel(overallScore);
  const colors = getConfidenceColor(level);

  return (
    <div className={`p-6 rounded-xl border-2 ${colors.border} ${colors.bg} ${className}`}>
      <div className="text-center mb-6">
        <div className={`w-16 h-16 mx-auto rounded-full bg-gradient-to-br ${colors.gradient} flex items-center justify-center mb-3`}>
          <Award className="w-8 h-8 text-white" />
        </div>
        <h2 className={`text-2xl font-bold ${colors.text} mb-2`}>Puntuación General</h2>
        <div className="text-4xl font-bold text-gray-900 mb-2">{overallScore}%</div>
        <ConfidenceBadge level={level} size="lg" />
      </div>

      <div className="space-y-3">
        <h3 className={`font-semibold ${colors.text} mb-3`}>Desglose por Categoría:</h3>
        {breakdown.map((item, index) => (
          <div key={index} className="flex items-center justify-between p-3 bg-white bg-opacity-50 rounded-lg">
            <div className="flex-1">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium text-gray-700">{item.category}</span>
                <span className="text-sm text-gray-600">Peso: {item.weight}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 bg-gradient-to-r ${getConfidenceColor(calculateConfidenceLevel(item.score)).gradient} rounded-full`}
                  style={{ width: `${item.score}%` }}
                />
              </div>
            </div>
            <div className="ml-4 text-right">
              <div className="text-lg font-bold text-gray-900">{item.score}%</div>
              <ConfidenceBadge level={calculateConfidenceLevel(item.score)} size="sm" showIcon={false} />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default {
  ConfidenceBadge,
  ConfidenceBar,
  ConfidenceCard,
  ConfidenceGrid,
  ConfidenceSummary,
  calculateConfidenceLevel,
  getConfidenceColor,
  getConfidenceIcon
};
