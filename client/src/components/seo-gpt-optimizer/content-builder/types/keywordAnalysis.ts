/**
 * TypeScript interfaces for keyword analysis and research data
 * Replaces 'any' types with properly defined structures
 */

export interface GoogleResult {
  domain?: string;
  title?: string;
  snippet?: string;
  url?: string;
  position?: number;
}

export interface SerpFeatures {
  related_searches?: string[];
  people_also_ask?: Array<{
    question: string;
    snippet?: string;
  }>;
}

export interface GoogleResults {
  results?: GoogleResult[];
  serp_features?: SerpFeatures;
}

export interface SocialInsight {
  title?: string;
  url?: string;
  score?: number;
  comments?: number;
  views?: number;
  question?: string;
}

export interface SocialInsights {
  reddit?: SocialInsight[];
  quora?: SocialInsight[];
}

export interface ResearchSummary {
  priority_keywords?: string[];
  research_confidence?: number;
  target_audience?: string;
  content_length_recommendation?: number;
  tone_preference?: string;
}

export interface EntitiesAndQuestions {
  entities?: string[];
  common_questions?: string[];
  related_topics?: string[];
}

export interface ResearchData {
  research_summary?: ResearchSummary;
  google_results?: GoogleResults;
  social_insights?: SocialInsights;
  entities_and_questions?: EntitiesAndQuestions;
  search_intent?: {
    primary_intent: string;
    confidence: number;
    secondary_intents?: string[];
  };
  content_opportunities?: {
    gaps?: string[];
    angles?: string[];
    formats?: string[];
  };
}

export interface BlogMetrics {
  contentOpportunity: number; // 0-100 score for content gap
  topicPopularity: number; // 0-100 based on SERP features
  searchIntent: 'informational' | 'commercial' | 'navigational';
  recommendedFormat: string; // Content format recommendation
  questionsToAnswer: string[]; // From People Also Ask
  competitorInsights: {
    majorCompetitors: number;
    blogOpportunities: number;
    avgContentLength: number;
  };
}

export interface KeywordMetrics {
  keyword: string;
  searchVolume: number; // 0 for blog analysis (not relevant)
  competition: 'low' | 'medium' | 'high';
  difficulty: number; // 0-100 ranking difficulty
  cpc: number; // 0 for blog analysis (not relevant)
  trend: 'up' | 'down' | 'stable';
  confidence: 'Excelente' | 'Bueno' | 'Mejorable';
  opportunity: number; // 0-100 overall blog opportunity score
  relatedTerms: string[];
  dataSource: 'research_engine' | 'fallback' | 'cached';
  lastUpdated: Date;
  blogMetrics?: BlogMetrics; // Blog-specific analysis
}

export interface KeywordAnalysisConfig {
  VOLUME_MULTIPLIERS: {
    REDDIT: number;
    QUORA: number;
    SERP: number;
  };
  VOLUME_LIMITS: {
    MIN: number;
    MAX: number;
  };
  COMPETITION_THRESHOLDS: {
    HIGH: number;
    MEDIUM: number;
  };
  ANALYSIS_DELAY: number;
  MAX_KEYWORDS: number;
  CACHE_DURATION: number;
}

export interface KeywordAnalysisError {
  type: 'api_error' | 'network_error' | 'validation_error' | 'unknown_error';
  message: string;
  details?: any;
  timestamp: Date;
  retryable: boolean;
}

export interface KeywordAnalysisState {
  metrics: KeywordMetrics[];
  isAnalyzing: boolean;
  error: KeywordAnalysisError | null;
  lastAnalysis: Date | null;
  cacheHit: boolean;
}

export interface BlogPlanningData {
  topic: string;
  keywords: string[];
  targetKeywords: string[];
  suggestedKeywords: string[];
  researchData?: ResearchData;
  confidence: number;
  estimatedTraffic: number;
}

export interface DebugInfo {
  timestamp: Date;
  component: string;
  action: string;
  data?: any;
  performance?: {
    startTime: number;
    endTime: number;
    duration: number;
  };
}

// Configuration constants
export const KEYWORD_ANALYSIS_CONFIG: KeywordAnalysisConfig = {
  VOLUME_MULTIPLIERS: {
    REDDIT: 150,    // Reddit discussions indicate moderate search interest
    QUORA: 200,     // Quora questions indicate higher search intent
    SERP: 100       // SERP results indicate baseline competition
  },
  VOLUME_LIMITS: {
    MIN: 500,       // Minimum viable search volume
    MAX: 15000      // Maximum realistic estimate from social signals
  },
  COMPETITION_THRESHOLDS: {
    HIGH: 5,        // 5+ authoritative domains = high competition
    MEDIUM: 2       // 2-4 authoritative domains = medium competition
  },
  ANALYSIS_DELAY: 1000,     // Debounce delay for topic changes
  MAX_KEYWORDS: 15,         // Maximum keywords to analyze per topic
  CACHE_DURATION: 24 * 60 * 60 * 1000  // 24 hours cache duration
};

// Authoritative domain patterns for competition analysis
export const AUTHORITATIVE_DOMAINS = [
  'wikipedia',
  '.gov',
  '.edu',
  'youtube.com',
  'amazon.com',
  'linkedin.com',
  'facebook.com',
  'twitter.com',
  'instagram.com'
];

// Topic variation templates for keyword generation
export const TOPIC_VARIATION_TEMPLATES = {
  es: [
    '{topic} guía',
    'cómo {topic}',
    '{topic} tutorial',
    '{topic} consejos',
    '{topic} beneficios',
    '{topic} paso a paso',
    'qué es {topic}',
    '{topic} para principiantes',
    'mejores {topic}',
    '{topic} gratis'
  ],
  en: [
    '{topic} guide',
    'how to {topic}',
    '{topic} tutorial',
    '{topic} tips',
    '{topic} benefits',
    'what is {topic}',
    '{topic} for beginners',
    'best {topic}',
    'free {topic}'
  ]
};

export default {
  KEYWORD_ANALYSIS_CONFIG,
  AUTHORITATIVE_DOMAINS,
  TOPIC_VARIATION_TEMPLATES
};
