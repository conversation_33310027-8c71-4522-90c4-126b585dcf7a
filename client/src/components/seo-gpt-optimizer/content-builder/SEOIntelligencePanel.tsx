/**
 * SEO Intelligence Panel - Real-time SEO analysis and optimization
 * Provides live SEO scoring, suggestions, and content generation
 */

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain, Wand2, Bar<PERSON>hart3, Lightbulb, <PERSON>rk<PERSON>, CheckCircle
} from 'lucide-react';
import { seoIntelligenceService } from '../../../services/seoIntelligenceService';
import BlogPlanningInterface from './BlogPlanningInterface';
import SEOAnalysisResults from './components/SEOAnalysisResults';
import KeywordAnalysisErrorBoundary from './components/KeywordAnalysisErrorBoundary';
import { useSEOAnalysis } from './hooks/useSEOAnalysis';
import { debugLogger, logUserAction } from './utils/debugLogger';

interface SEOIntelligencePanelProps {
  content: string;
  onContentGenerate: (content: string) => void;
  projectId?: string;
  className?: string;
}

/**
 * SEOIntelligencePanel Component
 *
 * Main panel for SEO analysis and content generation with comprehensive
 * error handling, logging, and user experience improvements.
 *
 * Features:
 * - Real-time SEO analysis with caching
 * - Blog planning interface integration
 * - Comprehensive error boundaries
 * - Detailed logging and debugging
 * - Performance monitoring
 *
 * @param content - Content to analyze
 * @param projectId - Project identifier for context
 * @param onContentGenerate - Callback for generated content
 * @param className - Additional CSS classes
 */
const SEOIntelligencePanel: React.FC<SEOIntelligencePanelProps> = ({
  content,
  projectId = 'default',
  onContentGenerate,
  className = ''
}) => {
  debugLogger.info('SEOIntelligencePanel', 'Component initialized', {
    projectId,
    contentLength: content.length,
    hasContent: !!content.trim()
  });

  // Use the custom SEO analysis hook
  const {
    analysis,
    isAnalyzing,
    analyzeContent,
    generateSuggestions
  } = useSEOAnalysis({
    autoAnalyze: false,
    debounceMs: 1000,
    cacheEnabled: true
  });

  // UI state
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState<'analysis' | 'generate' | 'suggestions'>('analysis');
  const [showBlogPlanner, setShowBlogPlanner] = useState(false);

  /**
   * Handle manual analysis trigger
   */
  const handleAnalyze = useCallback(() => {
    logUserAction('SEOIntelligencePanel', 'manual_analysis_trigger', {
      contentLength: content.length,
      activeTab
    });
    analyzeContent(content, undefined, true);
  }, [content, activeTab, analyzeContent]);

  /**
   * Handle suggestions generation
   */
  const handleGenerateSuggestions = useCallback(() => {
    logUserAction('SEOIntelligencePanel', 'generate_suggestions', {
      contentLength: content.length
    });
    generateSuggestions(content);
  }, [content, generateSuggestions]);

  const generateOptimizedContent = useCallback(async () => {
    // Open the blog planning interface instead of direct generation
    setShowBlogPlanner(true);
  }, []);

  const handleBlogPlanningComplete = useCallback(async (planningData: any) => {
    setIsGenerating(true);
    try {
      // Enhanced content generation with research-backed keywords
      const generatedContent = await seoIntelligenceService.generateContent({
        topic: planningData.topic,
        keywords: planningData.keywords,
        contentType: 'educational',
        targetLength: 1200,
        tone: 'professional',
        includeImages: false
      });

      onContentGenerate(generatedContent.content);
      setShowBlogPlanner(false);
    } catch (error) {
      console.error('Failed to generate content:', error);
    } finally {
      setIsGenerating(false);
    }
  }, [onContentGenerate]);



  console.log('🔥 SEO INTELLIGENCE PANEL - Rendering');

  return (
    <div className={`${className}`}>
      {/* Header */}
      <div className="mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-500 rounded-lg">
            <Brain className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Emma SEO Intelligence</h3>
            <p className="text-xs text-gray-600">Análisis en tiempo real</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex">
        {[
          { id: 'analysis', label: 'Análisis', icon: BarChart3 },
          { id: 'generate', label: 'Generar', icon: Wand2 },
          { id: 'suggestions', label: 'Sugerencias', icon: Lightbulb }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-semibold transition-all duration-200 rounded-lg mx-1 ${
              activeTab === tab.id
                ? 'text-white bg-blue-500'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="p-4 max-h-[600px] overflow-y-auto">
        <AnimatePresence mode="wait">
          {activeTab === 'analysis' && (
            <motion.div
              key="analysis"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="space-y-4"
            >
              <KeywordAnalysisErrorBoundary>
                <SEOAnalysisResults
                  analysis={analysis}
                  isAnalyzing={isAnalyzing}
                  onAnalyze={handleAnalyze}
                  onGenerateSuggestions={handleGenerateSuggestions}
                />
              </KeywordAnalysisErrorBoundary>
            </motion.div>
          )}

          {activeTab === 'generate' && (
            <motion.div
              key="generate"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="space-y-4"
            >
              <div className="text-center">
                <Sparkles className="w-12 h-12 mx-auto mb-3 text-[#3018ef]" />
                <h4 className="font-semibold text-gray-900 mb-2">Generación Inteligente</h4>
                <p className="text-sm text-gray-600 mb-6">
                  Crea contenido optimizado para SEO y SAIO automáticamente
                </p>
              </div>

              <div className="space-y-3">
                <button
                  onClick={generateOptimizedContent}
                  disabled={isGenerating}
                  className="w-full p-4 bg-green-500 hover:bg-green-600 text-white rounded-xl disabled:opacity-50 transition-all duration-200 flex items-center justify-center gap-3 font-semibold"
                >
                  {isGenerating ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Generando...
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-4 h-4" />
                      Generar Blog SEO Completo
                    </>
                  )}
                </button>
              </div>
            </motion.div>
          )}

          {activeTab === 'suggestions' && (
            <motion.div
              key="suggestions"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="space-y-4"
            >
              {/* Manual Analysis Trigger */}
              {!content.trim() ? (
                <div className="text-center py-12 text-gray-500">
                  <div className="w-16 h-16 mx-auto mb-4 bg-purple-100 rounded-full flex items-center justify-center">
                    <Lightbulb className="w-8 h-8 text-[#3018ef]" />
                  </div>
                  <h3 className="font-medium text-gray-900 mb-2">Sugerencias Inteligentes</h3>
                  <p className="text-sm text-gray-600 max-w-sm mx-auto">
                    Escribe tu contenido y obtén sugerencias específicas y accionables para mejorarlo
                  </p>
                </div>
              ) : !analysis?.suggestions?.length ? (
                <div className="text-center py-8">
                  <div className="mb-6">
                    <div className="w-12 h-12 mx-auto mb-3 bg-purple-100 rounded-full flex items-center justify-center">
                      <Lightbulb className="w-6 h-6 text-[#3018ef]" />
                    </div>
                    <h3 className="font-medium text-gray-900 mb-2">Análisis Contextual</h3>
                    <p className="text-sm text-gray-600 mb-4 max-w-md mx-auto">
                      Nuestro sistema analizará tu contenido específico y te dará recomendaciones personalizadas
                    </p>
                  </div>

                  <button
                    onClick={handleGenerateSuggestions}
                    disabled={isAnalyzing}
                    className="inline-flex items-center gap-2 px-8 py-3 bg-purple-500 hover:bg-purple-600 text-white rounded-xl transition-all duration-200 disabled:opacity-50 font-semibold"
                  >
                    {isAnalyzing ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Analizando tu contenido...
                      </>
                    ) : (
                      <>
                        <Lightbulb className="w-5 h-5" />
                        Generar Sugerencias Personalizadas
                      </>
                    )}
                  </button>

                  <div className="mt-4 flex items-center justify-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Análisis contextual
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Sugerencias específicas
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      Accionables
                    </div>
                  </div>
                </div>
              ) : (
                <>
                  {/* Suggestions Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Lightbulb className="w-4 h-4 text-[#3018ef]" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">Sugerencias Personalizadas</h4>
                        <p className="text-xs text-gray-500">Basadas en tu contenido específico</p>
                      </div>
                    </div>
                    <button
                      onClick={handleGenerateSuggestions}
                      disabled={isAnalyzing}
                      className="text-xs text-blue-500 hover:text-blue-600 transition-colors px-3 py-1 rounded-lg hover:bg-gray-50 font-medium"
                    >
                      {isAnalyzing ? 'Analizando...' : 'Actualizar'}
                    </button>
                  </div>

                  {/* Suggestions List */}
                  <div className="space-y-4">
                    {analysis.suggestions.map((suggestion, index) => {
                      // Parse the suggestion to extract title and content
                      const lines = suggestion.split('\n');
                      const title = lines[0]?.replace(/^\*\*|\*\*$/g, '').replace(/^[📝🏗️✂️❓📋📞💫🎯📈⚠️🔗]+\s*/, '') || '';
                      const content = lines.slice(1).join('\n');

                      return (
                        <div
                          key={index}
                          className="group relative p-5 bg-yellow-50 border border-yellow-200 rounded-xl hover:bg-yellow-100 transition-all duration-300"
                        >
                          <div className="flex items-start gap-4">
                            <div className="w-8 h-8 bg-yellow-500 rounded-xl flex items-center justify-center flex-shrink-0">
                              <Lightbulb className="w-4 h-4 text-white" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4 className="text-sm font-semibold text-gray-900 mb-2 leading-tight">
                                {title}
                              </h4>
                              <div className="text-xs text-gray-700 leading-relaxed space-y-2">
                                {content.split('\n\n').map((section, sectionIndex) => {
                                  if (!section.trim()) return null;

                                  // Handle different section types
                                  if (section.includes('**¿Por qué?**')) {
                                    const text = section.replace('**¿Por qué?**', '').trim();
                                    return (
                                      <div key={sectionIndex} className="bg-blue-50/50 p-3 rounded-lg border-l-3 border-blue-400">
                                        <div className="flex items-start gap-2">
                                          <span className="text-blue-600 font-medium text-xs">¿Por qué?</span>
                                          <span className="text-blue-800 text-xs">{text}</span>
                                        </div>
                                      </div>
                                    );
                                  } else if (section.includes('**Cómo hacerlo:**')) {
                                    const text = section.replace('**Cómo hacerlo:**', '').trim();
                                    const steps = text.split('•').filter(step => step.trim());
                                    return (
                                      <div key={sectionIndex} className="bg-green-50/50 p-3 rounded-lg border-l-3 border-green-400">
                                        <div className="text-green-600 font-medium text-xs mb-2">Cómo hacerlo:</div>
                                        <ul className="space-y-1">
                                          {steps.map((step, stepIndex) => (
                                            <li key={stepIndex} className="flex items-start gap-2 text-green-800 text-xs">
                                              <span className="w-1 h-1 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                                              <span>{step.trim()}</span>
                                            </li>
                                          ))}
                                        </ul>
                                      </div>
                                    );
                                  } else if (section.includes('**Impacto SEO:**')) {
                                    const text = section.replace('**Impacto SEO:**', '').trim();
                                    return (
                                      <div key={sectionIndex} className="bg-purple-50/50 p-3 rounded-lg border-l-3 border-purple-400">
                                        <div className="flex items-start gap-2">
                                          <span className="text-purple-600 font-medium text-xs">Impacto SEO:</span>
                                          <span className="text-purple-800 text-xs font-medium">{text}</span>
                                        </div>
                                      </div>
                                    );
                                  }

                                  return (
                                    <p key={sectionIndex} className="text-gray-600 text-xs">
                                      {section.trim()}
                                    </p>
                                  );
                                })}
                              </div>
                            </div>
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Action Footer */}
                  <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
                    <div className="flex items-center gap-2 text-sm text-blue-800">
                      <CheckCircle className="w-4 h-4 text-blue-600" />
                      <span className="font-medium">
                        {analysis.suggestions.length} sugerencias específicas generadas
                      </span>
                    </div>
                    <p className="text-xs text-blue-600 mt-1">
                      Implementa estas mejoras para optimizar tu contenido SEO
                    </p>
                  </div>
                </>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Blog Planning Interface */}
      <BlogPlanningInterface
        projectId={projectId}
        isVisible={showBlogPlanner}
        onComplete={handleBlogPlanningComplete}
        onCancel={() => setShowBlogPlanner(false)}
      />
    </div>
  );
};

export default SEOIntelligencePanel;
