/**
 * Custom hook for SEO analysis business logic
 * Extracts SEO analysis logic from components for better maintainability
 */

import { useState, useCallback, useRef } from 'react';
import { seoIntelligenceService, SEOAnalysis } from '../../../../services/seoIntelligenceService';
import { debugLogger, startTiming, endTiming, logApiCall } from '../utils/debugLogger';

interface SEOAnalysisState {
  analysis: SEOAnalysis | null;
  isAnalyzing: boolean;
  error: string | null;
  lastAnalyzed: Date | null;
}

interface SEOAnalysisOptions {
  autoAnalyze?: boolean;
  debounceMs?: number;
  cacheEnabled?: boolean;
}

/**
 * Custom hook for managing SEO analysis state and operations
 * 
 * Features:
 * - Comprehensive error handling
 * - Performance monitoring and logging
 * - Debounced analysis to prevent excessive API calls
 * - Caching mechanism for recent analyses
 * - Detailed debugging information
 * 
 * @param options - Configuration options for the hook
 * @returns Object containing analysis state and control functions
 */
export const useSEOAnalysis = (options: SEOAnalysisOptions = {}) => {
  const {
    autoAnalyze = false,
    debounceMs = 1000,
    cacheEnabled = true
  } = options;

  const [state, setState] = useState<SEOAnalysisState>({
    analysis: null,
    isAnalyzing: false,
    error: null,
    lastAnalyzed: null
  });

  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const cacheRef = useRef(new Map<string, { analysis: SEOAnalysis; timestamp: number }>());
  const abortControllerRef = useRef<AbortController | null>(null);

  /**
   * Generate cache key for content
   */
  const generateCacheKey = useCallback((content: string, keywords: string[]): string => {
    const contentHash = content.slice(0, 100); // Use first 100 chars as simple hash
    const keywordsHash = keywords.join(',');
    return `${contentHash}-${keywordsHash}`;
  }, []);

  /**
   * Check cache for existing analysis
   */
  const getCachedAnalysis = useCallback((content: string, keywords: string[]): SEOAnalysis | null => {
    if (!cacheEnabled) return null;

    const cacheKey = generateCacheKey(content, keywords);
    const cached = cacheRef.current.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) { // 5 minutes cache
      debugLogger.debug('SEOAnalysis', 'Cache hit for analysis', { cacheKey, age: Date.now() - cached.timestamp });
      return cached.analysis;
    }

    if (cached) {
      debugLogger.debug('SEOAnalysis', 'Cache expired for analysis', { cacheKey, age: Date.now() - cached.timestamp });
      cacheRef.current.delete(cacheKey);
    }

    return null;
  }, [cacheEnabled, generateCacheKey]);

  /**
   * Store analysis in cache
   */
  const setCachedAnalysis = useCallback((content: string, keywords: string[], analysis: SEOAnalysis) => {
    if (!cacheEnabled) return;

    const cacheKey = generateCacheKey(content, keywords);
    cacheRef.current.set(cacheKey, {
      analysis,
      timestamp: Date.now()
    });

    debugLogger.debug('SEOAnalysis', 'Analysis cached', { cacheKey, overallScore: analysis.overallScore });
  }, [cacheEnabled, generateCacheKey]);

  /**
   * Extract keywords from content for analysis
   */
  const extractKeywordsFromContent = useCallback((content: string): string[] => {
    if (!content.trim()) return [];

    const words = content
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Remove punctuation
      .split(/\s+/)
      .filter(word => word.length > 3) // Filter short words
      .filter(word => !['para', 'esta', 'este', 'como', 'pero', 'todo', 'cada', 'desde', 'hasta'].includes(word)); // Filter common Spanish stop words

    // Get most frequent words
    const wordCount = words.reduce((acc, word) => {
      acc[word] = (acc[word] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(wordCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
  }, []);

  /**
   * Perform SEO analysis on content
   */
  const analyzeContent = useCallback(async (
    content: string, 
    keywords?: string[], 
    forceAnalysis = false
  ): Promise<void> => {
    if (!content.trim()) {
      setState(prev => ({
        ...prev,
        analysis: null,
        error: null
      }));
      return;
    }

    const analysisTimingId = startTiming('SEOAnalysis', 'analyzeContent');

    // Cancel any existing analysis
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();

    setState(prev => ({
      ...prev,
      isAnalyzing: true,
      error: null
    }));

    try {
      // Extract keywords if not provided
      const analysisKeywords = keywords && keywords.length > 0 
        ? keywords 
        : extractKeywordsFromContent(content);

      debugLogger.info('SEOAnalysis', 'Starting content analysis', {
        contentLength: content.length,
        keywordsCount: analysisKeywords.length,
        keywords: analysisKeywords,
        forceAnalysis
      });

      // Check cache first (unless forced)
      if (!forceAnalysis) {
        const cachedAnalysis = getCachedAnalysis(content, analysisKeywords);
        if (cachedAnalysis) {
          setState(prev => ({
            ...prev,
            analysis: cachedAnalysis,
            isAnalyzing: false,
            lastAnalyzed: new Date()
          }));
          
          endTiming(analysisTimingId, { source: 'cache', overallScore: cachedAnalysis.overallScore });
          return;
        }
      }

      // Perform analysis
      const apiTimingId = startTiming('SEOAnalysisAPI', 'analyzeContent');
      const analysis = await seoIntelligenceService.analyzeContent(
        content,
        analysisKeywords,
        forceAnalysis
      );
      endTiming(apiTimingId, { overallScore: analysis.overallScore });

      logApiCall('SEOAnalysis', 'seoIntelligenceService.analyzeContent', 
        { contentLength: content.length, keywordsCount: analysisKeywords.length }, 
        analysis
      );

      // Cache the result
      setCachedAnalysis(content, analysisKeywords, analysis);

      setState(prev => ({
        ...prev,
        analysis,
        isAnalyzing: false,
        error: null,
        lastAnalyzed: new Date()
      }));

      debugLogger.info('SEOAnalysis', 'Analysis completed successfully', {
        overallScore: analysis.overallScore,
        suggestionsCount: analysis.suggestions?.length || 0,
        scores: analysis.scores
      });

      endTiming(analysisTimingId, { 
        source: 'api', 
        overallScore: analysis.overallScore,
        success: true 
      });

    } catch (error: any) {
      if (error.name === 'AbortError') {
        debugLogger.info('SEOAnalysis', 'Analysis aborted by user');
        return;
      }

      debugLogger.error('SEOAnalysis', 'Analysis failed', error, {
        contentLength: content.length,
        keywordsCount: keywords?.length || 0
      });

      setState(prev => ({
        ...prev,
        isAnalyzing: false,
        error: error.message || 'Error desconocido durante el análisis SEO'
      }));

      endTiming(analysisTimingId, { 
        source: 'api', 
        error: true,
        errorType: error.name || 'UnknownError'
      });
    }
  }, [extractKeywordsFromContent, getCachedAnalysis, setCachedAnalysis]);

  /**
   * Debounced analysis for auto-analysis scenarios
   */
  const debouncedAnalyze = useCallback((
    content: string, 
    keywords?: string[], 
    forceAnalysis = false
  ) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      debugLogger.debug('SEOAnalysis', 'Debounced analysis triggered', { 
        contentLength: content.length,
        debounceMs 
      });
      analyzeContent(content, keywords, forceAnalysis);
    }, debounceMs);
  }, [analyzeContent, debounceMs]);

  /**
   * Generate contextual suggestions
   */
  const generateSuggestions = useCallback(async (content: string): Promise<void> => {
    debugLogger.info('SEOAnalysis', 'Generating contextual suggestions', { contentLength: content.length });
    
    // Trigger deep analysis for suggestions
    await analyzeContent(content, undefined, true);
  }, [analyzeContent]);

  /**
   * Clear current analysis
   */
  const clearAnalysis = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    setState({
      analysis: null,
      isAnalyzing: false,
      error: null,
      lastAnalyzed: null
    });

    debugLogger.info('SEOAnalysis', 'Analysis cleared');
  }, []);

  /**
   * Retry failed analysis
   */
  const retryAnalysis = useCallback((content: string, keywords?: string[]) => {
    debugLogger.info('SEOAnalysis', 'Retrying analysis', { contentLength: content.length });
    return analyzeContent(content, keywords, true);
  }, [analyzeContent]);

  /**
   * Clear cache
   */
  const clearCache = useCallback(() => {
    cacheRef.current.clear();
    debugLogger.info('SEOAnalysis', 'Cache cleared');
  }, []);

  return {
    // State
    analysis: state.analysis,
    isAnalyzing: state.isAnalyzing,
    error: state.error,
    lastAnalyzed: state.lastAnalyzed,

    // Actions
    analyzeContent,
    debouncedAnalyze,
    generateSuggestions,
    clearAnalysis,
    retryAnalysis,
    clearCache,

    // Utilities
    extractKeywordsFromContent
  };
};
