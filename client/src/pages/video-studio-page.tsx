import React from "react";
import { useLocation } from "wouter";
import { motion } from "framer-motion";
import { FocusCards } from "@/components/ui/focus-cards";
import DashboardLayout from "@/components/layout/dashboard-layout";
import {
  Video,
  Wand2,
  Camera,
  Film,
  Sparkles,
  Zap,
  Play,
  Clapperboard
} from "lucide-react";

function VideoStudioContent() {
  const [, navigate] = useLocation();

  const videoServices = [
    {
      title: "LumaLabs Dream Machine",
      src: "https://images.unsplash.com/photo-1536440136628-849c177e76a1?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      videoSrc: "/video gato brincando.mp4",
      poster: "/Gato lentes.webp",
      onClick: () => navigate("/video-studio/luma-labs")
    },
    {
      title: "Google Veo",
      src: "https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/video-studio/google-veo")
    },
    {
      title: "Runway ML",
      src: "https://images.unsplash.com/photo-1485846234645-a62644f84728?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/video-studio/runway-ml")
    },
    {
      title: "Texto a Video",
      src: "https://images.unsplash.com/photo-1516035069371-29a1b244cc32?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/video-studio/text-to-video")
    },
    {
      title: "Imagen a Video",
      src: "https://images.unsplash.com/photo-1440404653325-ab127d49abc1?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/image-to-video")
    },
    {
      title: "Editor de Video IA",
      src: "https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/video-editor")
    },
    {
      title: "Generador de Shorts",
      src: "https://images.unsplash.com/photo-1611162617474-5b21e879e113?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/video-studio/shorts-generator")
    },
    {
      title: "Video Automático",
      src: "https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/video-generator")
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Hero Section - Mismo estilo que Vibe Marketing */}
      <div className="relative rounded-2xl overflow-hidden mb-8 backdrop-blur-xl">
        <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef] via-[#4f46e5] to-[#dd3a5a] opacity-95"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
        <div className="relative px-8 py-16 md:py-20 md:px-12">
          <div className="max-w-5xl">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
              className="mb-8"
            >
              <motion.span
                className="inline-flex items-center bg-white/20 backdrop-blur-md text-white font-semibold px-6 py-3 rounded-full mb-6 border border-white/30"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <Sparkles className="inline-block w-5 h-5 mr-2" />
                Potenciado con IA
              </motion.span>
              <h1 className="text-4xl md:text-5xl lg:text-7xl font-black text-white mb-6 leading-tight">
                Video{" "}
                <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                  Studio
                </span>
                <br className="hidden md:block" />
                <span className="text-white/90 font-light">
                  creación de video con IA
                </span>
              </h1>
              <p className="text-white/90 text-xl md:text-2xl max-w-3xl font-light leading-relaxed">
                Crea videos increíbles con inteligencia artificial. Desde texto hasta video,
                edición profesional y generación automática de contenido.
              </p>
            </motion.div>
          </div>
        </div>

        {/* Floating elements with glassmorphism */}
        <motion.div
          className="absolute right-0 bottom-0 transform translate-y-1/4 -translate-x-10 hidden lg:block"
          initial={{ opacity: 0, x: 100, rotate: -10 }}
          animate={{ opacity: 1, x: 0, rotate: 0 }}
          transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
        >
          <div className="relative w-72 h-72">
            <motion.div
              className="absolute w-40 h-40 bg-white/20 backdrop-blur-md rounded-2xl -top-32 -left-20 transform rotate-12 flex items-center justify-center shadow-2xl border border-white/30"
              animate={{ rotate: [12, 18, 12] }}
              transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
            >
              <span className="text-7xl">🎬</span>
            </motion.div>
            <motion.div
              className="absolute w-44 h-44 bg-white/15 backdrop-blur-md rounded-2xl -top-10 left-20 transform -rotate-6 flex items-center justify-center shadow-2xl border border-white/20"
              animate={{ rotate: [-6, -12, -6] }}
              transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
            >
              <span className="text-7xl">🎥</span>
            </motion.div>
            <motion.div
              className="absolute w-36 h-36 bg-white/25 backdrop-blur-md rounded-2xl top-24 -left-10 transform rotate-45 flex items-center justify-center shadow-2xl border border-white/40"
              animate={{ rotate: [45, 50, 45] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 2 }}
            >
              <span className="text-6xl">✨</span>
            </motion.div>
          </div>
        </motion.div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-12">

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="text-3xl font-bold text-purple-600 mb-2">8+</div>
            <div className="text-gray-600">Servicios de Video IA</div>
          </div>
          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="text-3xl font-bold text-blue-600 mb-2">HD</div>
            <div className="text-gray-600">Calidad Profesional</div>
          </div>
          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="text-3xl font-bold text-green-600 mb-2">24/7</div>
            <div className="text-gray-600">Disponibilidad</div>
          </div>
        </motion.div>

        {/* Services Grid */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">
            Servicios Disponibles
          </h2>
          <FocusCards cards={videoServices} />
        </motion.div>

        {/* Features Section */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="bg-purple-100 p-3 rounded-full w-fit mx-auto mb-4">
              <Wand2 className="w-8 h-8 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">IA Avanzada</h3>
            <p className="text-gray-600 text-sm">
              Tecnología de última generación para crear videos profesionales
            </p>
          </div>

          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="bg-blue-100 p-3 rounded-full w-fit mx-auto mb-4">
              <Zap className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Rápido</h3>
            <p className="text-gray-600 text-sm">
              Genera videos en minutos, no en horas
            </p>
          </div>

          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="bg-green-100 p-3 rounded-full w-fit mx-auto mb-4">
              <Sparkles className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Calidad HD</h3>
            <p className="text-gray-600 text-sm">
              Videos en alta definición listos para publicar
            </p>
          </div>

          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="bg-orange-100 p-3 rounded-full w-fit mx-auto mb-4">
              <Play className="w-8 h-8 text-orange-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Fácil de Usar</h3>
            <p className="text-gray-600 text-sm">
              Interfaz intuitiva para todos los niveles
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

export default function VideoStudioPage() {
  return (
    <DashboardLayout pageTitle="Video Studio">
      <VideoStudioContent />
    </DashboardLayout>
  );
}
