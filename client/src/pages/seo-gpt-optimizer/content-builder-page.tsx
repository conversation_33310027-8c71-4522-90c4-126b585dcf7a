/**
 * SEO & GPT Optimizer™ - Content Builder Page
 * Main page for the content builder with real-time analysis
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Save } from 'lucide-react';
import { useLocation, useRoute } from 'wouter';
import toast from 'react-hot-toast';

import { useProjects } from '../../hooks/seo-gpt-optimizer/useProjects';
import { seoGptAPI } from '../../services/seo-gpt-optimizer/api';

import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';
// import DebugPanel from '../../components/seo-gpt-optimizer/shared/DebugPanel';
import { ContentBuilder } from '../../components/seo-gpt-optimizer/content-builder';

const ContentBuilderPage: React.FC = () => {
  console.log('🔥 CONTENT BUILDER PAGE - Component initializing');

  const [location, setLocation] = useLocation();

  // Extract projectId and contentId from URL using wouter - now only edit mode since we always create first
  const [matchEdit, editParams] = useRoute('/dashboard/herramientas/seo-gpt-optimizer/projects/:projectId/edit/:contentId');

  const projectId = editParams?.projectId;
  const contentId = editParams?.contentId;

  console.log('🔥 ROUTE MATCH - Edit:', matchEdit);
  console.log('🔥 EXTRACTED - Project ID:', projectId, 'Content ID:', contentId);
  console.log('🔥 EDIT PARAMS:', editParams);
  console.log('🔥 CURRENT LOCATION:', location);

  // Validate project and content context
  useEffect(() => {
    if (!projectId || !contentId) {
      console.error('❌ CRITICAL: Missing required parameters');
      console.error('❌ Project ID:', projectId);
      console.error('❌ Content ID:', contentId);
      console.error('❌ Current location:', location);
    } else {
      console.log('✅ Context validated - Project:', projectId, 'Content:', contentId);
    }
  }, [projectId, contentId, location]);
  
  const [content, setContent] = useState('');
  const [topic, setTopic] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isCreatingContent, setIsCreatingContent] = useState(false);

  // Determine if this is create or edit mode
  const isEditMode = !!contentId;
  const isCreateMode = !contentId;

  console.log('🔥 CONTENT BUILDER PAGE - State - isLoading:', isLoading, 'projectId:', projectId);

  // Load existing content when in edit mode
  useEffect(() => {
    if (isEditMode && projectId && contentId) {
      console.log('🔄 EDIT MODE - Loading existing content:', contentId);
      setIsLoading(true);

      fetch(`/api/v1/seo-gpt/projects/${projectId}/contents/${contentId}`)
        .then(res => res.json())
        .then(data => {
          console.log('🔄 EDIT MODE - Loaded content:', data);
          if (data.status === 'success' && data.data) {
            setTopic(data.data.title || '');
            setContent(data.data.content_html || data.data.content_text || '');
            setHasUnsavedChanges(false);
          }
        })
        .catch(error => {
          console.error('❌ EDIT MODE - Error loading content:', error);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [projectId, contentId, isEditMode]);

  const {
    currentProject,
    loadProject,
    updateProject,
    loading: projectLoading
  } = useProjects();

  // Load project if projectId is provided
  useEffect(() => {
    if (projectId) {
      loadProject(projectId);
    }
  }, [projectId, loadProject]);



  // Set initial content from project
  useEffect(() => {
    if (currentProject) {
      setContent(currentProject.content_text || '');
      setTopic(currentProject.topic || '');
    }
  }, [currentProject]);

  const handleBack = () => {
    if (hasUnsavedChanges) {
      const confirmLeave = window.confirm(
        'Tienes cambios sin guardar. ¿Estás seguro de que quieres salir?'
      );
      if (!confirmLeave) return;
    }

    // Navigate back to the project dashboard using clean URL
    setLocation(`/dashboard/herramientas/seo-gpt-optimizer/projects/${projectId}`);
  };

  const handleSave = async (contentToSave: string) => {
    // 🔍 COMPREHENSIVE FRONTEND DEBUGGING
    console.log("🔥 SAVE DEBUG - Starting save process");
    console.log("🔥 SAVE DEBUG - projectId:", projectId);
    console.log("🔥 SAVE DEBUG - contentId:", contentId);
    console.log("🔥 SAVE DEBUG - isEditMode:", isEditMode);
    console.log("🔥 SAVE DEBUG - topic:", topic);
    console.log("🔥 SAVE DEBUG - raw contentToSave:", contentToSave);
    console.log("🔥 SAVE DEBUG - contentToSave length:", contentToSave.length);
    console.log("🔥 SAVE DEBUG - contentToSave type:", typeof contentToSave);

    if (!projectId) {
      console.error("❌ SAVE FAILED - No projectId");
      toast.error('Error: No se pudo identificar el proyecto');
      return;
    }

    if (!contentId) {
      console.error("❌ SAVE FAILED - No contentId");
      toast.error('Error: No se encontró el ID del contenido');
      return;
    }

    if (isSaving || isCreatingContent) {
      console.log("⚠️ SAVE SKIPPED - Already saving");
      return;
    }

    setIsSaving(true);
    console.log("🔄 SAVE - Setting isSaving to true");

    try {
      if (isEditMode && contentId) {
        // Update existing content
        console.log("🔥 EDIT MODE - Updating content:", contentId);

        // Extract text and HTML from content
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = contentToSave;
        const textContent = tempDiv.textContent || tempDiv.innerText || '';

        // 🔍 LOG EXTRACTED CONTENT
        console.log("🔄 CONTENT EXTRACTION:");
        console.log("   - Original HTML:", contentToSave.substring(0, 100) + "...");
        console.log("   - Extracted text:", textContent.substring(0, 100) + "...");
        console.log("   - Text length:", textContent.length);
        console.log("   - HTML length:", contentToSave.length);

        // 🔍 LOG API REQUEST PAYLOAD
        const requestPayload = {
          title: topic || 'Documento sin título',
          content_text: textContent, // Plain text version
          content_html: contentToSave, // HTML version
        };
        console.log("🚀 API REQUEST PAYLOAD:", requestPayload);
        console.log("🚀 API REQUEST URL:", `/projects/${projectId}/contents/${contentId}`);

        console.log("🔄 Making API call to updateProjectContent...");
        const response = await seoGptAPI.updateProjectContent(projectId, contentId, requestPayload);

        // 🔍 LOG API RESPONSE
        console.log("✅ API RESPONSE RECEIVED:", response);
        console.log("✅ Response status:", response.status);
        console.log("✅ Response data:", response.data);
        console.log("🔥 EDIT DEBUG - API Response:", response);

        if (response.status === 'success') {
          setHasUnsavedChanges(false);
          toast.success('Blog actualizado en el proyecto correctamente');
        } else {
          console.error("❌ API ERROR - Response status:", response.status);
          console.error("❌ API ERROR - Response message:", response.message);
          console.error("❌ API ERROR - Full response:", response);
          toast.error(`Error: ${response.message || 'Error al actualizar'}`);
          return; // Don't throw, just return
        }
      } else {
        // This should not happen anymore since we always create the blog first in the project dashboard
        console.error('❌ CRITICAL: No content ID found - this should not happen with new workflow');
        console.error('❌ Project ID:', projectId, 'Content ID:', contentId);
        throw new Error('No content ID found - cannot save without existing blog entry');
      }
    } catch (error) {
      // 🔍 COMPREHENSIVE ERROR LOGGING
      console.error("❌ SAVE ERROR - Full error object:", error);
      console.error("❌ SAVE ERROR - Error type:", typeof error);
      console.error("❌ SAVE ERROR - Error constructor:", error?.constructor?.name);

      if (error instanceof Error) {
        console.error("❌ SAVE ERROR - Error message:", error.message);
        console.error("❌ SAVE ERROR - Error stack:", error.stack);
      }

      // Log additional context
      console.error("❌ SAVE ERROR - Context:");
      console.error("   - projectId:", projectId);
      console.error("   - contentId:", contentId);
      console.error("   - isEditMode:", isEditMode);
      console.error("   - topic:", topic);

      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      console.error("❌ SAVE ERROR - Final error message:", errorMessage);
      toast.error(`Error al guardar: ${errorMessage}`);
    } finally {
      console.log("🔄 SAVE - Cleanup: setting isSaving and isCreatingContent to false");
      setIsSaving(false);
      setIsCreatingContent(false);
    }
  };

  const handleExport = () => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${topic || 'content'}-${Date.now()}.txt`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleToggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
  };

  // Sin keyboard shortcuts molestos

  if (!projectId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Proyecto no encontrado
          </h2>
          <p className="text-gray-600 mb-4">
            No se pudo identificar el proyecto.
          </p>
          <button
            onClick={() => setLocation('/dashboard/herramientas/seo-gpt-optimizer')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Volver al Dashboard
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Cargando contenido...
          </h2>
          <p className="text-gray-600">
            Por favor espera mientras cargamos tu contenido.
          </p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-full px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBack}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">
                    📁 {currentProject ? currentProject.title : 'Content Builder'}
                  </h1>
                  <p className="text-gray-600 text-sm">
                    {currentProject ? currentProject.topic : 'Crea contenido optimizado con análisis en tiempo real'}
                  </p>
                </div>
                {hasUnsavedChanges && (
                  <div className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-lg text-xs font-medium">
                    Cambios sin guardar
                  </div>
                )}
              </div>

              <div className="flex items-center gap-3">
                {/* Save Button */}
                <button
                  onClick={() => handleSave(content)}
                  disabled={!hasUnsavedChanges || isSaving}
                  className="flex items-center gap-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  {isSaving ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span className="text-sm">Guardando...</span>
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      <span className="text-sm">
                        {isEditMode ? 'Guardar Cambios' : 'Crear Blog'}
                      </span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content - Editor Simple */}
        <div className="h-[calc(100vh-80px)]">
          {console.log('🔥 CONTENT BUILDER PAGE - About to render ContentBuilder')}

          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-600">Cargando contenido...</p>
              </div>
            </div>
          ) : (
            <ContentBuilder
              projectId={projectId || 'demo'}
              initialContent={content}
              initialTopic={topic}
              onContentChange={(newContent) => {
                console.log('🔥 CONTENT BUILDER PAGE - Content changed:', newContent?.length);
                setContent(newContent);
                setHasUnsavedChanges(true);
              }}
              onSave={async (contentToSave, titleToSave) => {
                console.log('🔥 CONTENT BUILDER PAGE - Save triggered:', { contentLength: contentToSave?.length, title: titleToSave });
                setTopic(titleToSave);
                await handleSave(contentToSave);
              }}
              onTitleChange={(newTitle) => {
                console.log('🔥 CONTENT BUILDER PAGE - Title changed:', newTitle);
                setTopic(newTitle);
                setHasUnsavedChanges(true);
              }}
              className="h-full"
            />
          )}
        </div>

        {/* Debug Panel - Temporarily disabled */}
        {/* <DebugPanel
          title="Content Builder Debug"
          data={{
            projectId,
            contentId,
            isEditMode,
            isCreateMode,
            isLoading,
            isSaving,
            isCreatingContent,
            hasUnsavedChanges,
            contentLength: content?.length,
            topic,
            saveStatus: 'N/A',
            currentProject: currentProject?.title || 'None',
            projectLoading,
            location,
            matchEdit,
            editParams
          }}
        /> */}

      </div>
    </ErrorBoundary>
  );
};

export default ContentBuilderPage;
