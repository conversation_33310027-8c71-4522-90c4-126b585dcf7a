/**
 * SEO & GPT Optimizer™ - Project Dashboard Page
 * Dashboard individual para cada proyecto con todos sus contenidos
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import {
  ArrowLeft,
  Plus,
  FileText,
  Search,
  BarChart3,
  Folder,
  Calendar,
  Target,
  TrendingUp,
  Edit,
  Trash2,
  Eye
} from 'lucide-react';
import { useLocation, useRoute } from 'wouter';

import { useProjects } from '../../hooks/seo-gpt-optimizer/useProjects';
import { useSavedResearch } from '../../hooks/seo-gpt-optimizer/useSavedResearch';
import { seoGptAPI } from '../../services/seo-gpt-optimizer/api';
import { ProjectContent } from '../../types/seo-gpt-optimizer';
import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';
import LoadingSpinner from '../../components/seo-gpt-optimizer/shared/LoadingSpinner';

interface ProjectContent {
  id: string;
  title: string;
  type: 'blog' | 'research';
  status: 'draft' | 'completed' | 'published';
  word_count: number;
  gpt_rank_score: number;
  created_at: string;
  updated_at: string;
}

const ProjectDashboardPage: React.FC = () => {
  const [location, setLocation] = useLocation();
  const [match, params] = useRoute('/dashboard/herramientas/seo-gpt-optimizer/projects/:projectId');
  const projectId = params?.projectId;
  const lastLocationRef = useRef(location);

  console.log('🔥 PROJECT DASHBOARD - Match:', match, 'Project ID:', projectId);

  const { currentProject, loadProject, loading } = useProjects();
  const { getResearchByProject } = useSavedResearch();
  const [projectContents, setProjectContents] = useState<ProjectContent[]>([]);
  const [projectResearch, setProjectResearch] = useState<any[]>([]);
  const [contentLoading, setContentLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'all' | 'blogs' | 'research'>('all');

  // Combine project contents with research
  const allProjectContents = React.useMemo(() => {
    // Solo usar projectContents (que ahora incluye research guardado como contenido del proyecto)
    return projectContents || [];
  }, [projectContents]);

  // Load project contents from API
  const loadProjectContents = async (projectId: string) => {
    try {
      setContentLoading(true);
      console.log(`🔄 Loading contents for project: ${projectId}`);

      // Load project contents
      try {
        console.log('📄 Loading project contents for project:', projectId);
        const contentsResponse = await seoGptAPI.getProjectContents(projectId);
        console.log('📄 RAW API Response:', contentsResponse);
        console.log('📄 Response data:', contentsResponse.data);
        console.log('📄 Contents array:', contentsResponse.data?.contents);

        if (contentsResponse.status === 'success' && contentsResponse.data) {
          // Fix: Ajustar extracción según tu solución
          const payload = contentsResponse.data.data ?? contentsResponse.data;
          const contents = Array.isArray(payload) ? payload : payload.contents;
          setProjectContents(contents || []);
          console.log(`✅ SET PROJECT CONTENTS: ${(contents || []).length} items`);
          console.log('✅ Content titles:', (contents || []).map(c => c.title));
        } else {
          console.log('⚠️ No contents found or API error');
          setProjectContents([]);
        }
      } catch (contentsError) {
        console.error('❌ Error loading project contents:', contentsError);
        setProjectContents([]);
      }

      // Load project research
      try {
        console.log('🔬 Loading project research...');
        const researchResponse = await seoGptAPI.getProjectResearch(projectId);
        console.log('🔬 Research response:', researchResponse);

        if (researchResponse.status === 'success' && researchResponse.data) {
          setProjectResearch(researchResponse.data.research || []);
          console.log(`✅ Loaded ${researchResponse.data.research?.length || 0} research items`);
        } else {
          console.log('⚠️ No research found or API error');
          setProjectResearch([]);
        }
      } catch (researchError) {
        console.warn('❌ Error loading project research:', researchError);
        setProjectResearch([]);
      }

    } catch (error) {
      console.error('❌ Error loading project contents:', error);
      setProjectContents([]);
      setProjectResearch([]);
    } finally {
      setContentLoading(false);
      console.log('🏁 Finished loading project contents');
    }
  };



  // Load project data - exactly as you specified
  useEffect(() => {
    if (projectId) {
      console.log('🔄 Loading project data for:', projectId);
      loadProject(projectId);
      loadProjectContents(projectId);
    }
  }, [projectId]);

  // Also refresh content when component mounts (in case of direct navigation)
  useEffect(() => {
    if (projectId) {
      console.log('🔄 Component mounted - loading project contents...');
      loadProjectContents(projectId);
    }
  }, []); // Empty dependency array means this runs once on mount

  // Detect when user returns from editor and refresh content
  useEffect(() => {
    const currentLocation = location;
    const lastLocation = lastLocationRef.current;

    // Check if user is returning to project dashboard from editor
    const isReturningFromEditor =
      lastLocation.includes('/edit/') &&
      currentLocation.includes('/projects/') &&
      !currentLocation.includes('/edit/') &&
      projectId;

    if (isReturningFromEditor) {
      console.log('🔄 Returning from editor - refreshing project contents...');
      loadProjectContents(projectId);
    }

    // Update last location
    lastLocationRef.current = currentLocation;
  }, [location, projectId]);

  // Manual refresh function
  const handleRefreshContent = () => {
    if (projectId) {
      console.log('🔄 Manual refresh triggered...');
      setContentLoading(true);
      loadProjectContents(projectId);
      toast.success('Contenido actualizado');
    }
  };

  // Delete content function
  const handleDeleteContent = async (e: React.MouseEvent, contentId: string) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent event bubbling

    if (!window.confirm('¿Estás seguro de que quieres eliminar este contenido?')) {
      return;
    }

    try {
      console.log('🗑️ Deleting content:', contentId);
      const response = await fetch(`/api/v1/seo-gpt/projects/${projectId}/contents/${contentId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        console.log('✅ Content deleted successfully');
        toast.success('Contenido eliminado correctamente');
        // Reload contents
        loadProjectContents(projectId);
      } else {
        throw new Error(`Delete failed with status: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Error deleting content:', error);
      toast.error('Error al eliminar el contenido');
    }
  };

  // Edit content function
  const handleEditContent = (e: React.MouseEvent, contentId: string) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent event bubbling
    setLocation(`/dashboard/herramientas/seo-gpt-optimizer/projects/${projectId}/edit/${contentId}`);
  };

  const handleBack = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer/projects');
  };

  const handleCreateBlog = async () => {
    try {
      console.log('🔄 Creating new blog in project:', projectId);

      // First create the blog entry in the project (like Google Docs)
      const response = await fetch(`/api/v1/seo-gpt/projects/${projectId}/contents`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: 'Documento sin título',
          content_type: 'blog',
          content_text: '',
          content_html: '',
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to create blog: ${response.status}`);
      }

      const data = await response.json();
      const newBlogId = data.data.content_id;

      console.log('✅ Blog created in project:', newBlogId);
      toast.success('Nuevo blog creado en el proyecto');

      // Now navigate to edit the newly created blog
      setLocation(`/dashboard/herramientas/seo-gpt-optimizer/projects/${projectId}/edit/${newBlogId}`);

    } catch (error) {
      console.error('❌ Error creating blog:', error);
      toast.error('Error al crear el blog');
    }
  };

  const handleCreateResearch = () => {
    setLocation(`/dashboard/herramientas/seo-gpt-optimizer/research?project=${projectId}`);
  };

  const handleViewContent = (contentId: string, contentType: string) => {
    if (contentType === 'blog' || contentType === 'article') {
      setLocation(`/dashboard/herramientas/seo-gpt-optimizer/projects/${projectId}/edit/${contentId}`);
    } else if (contentType === 'research') {
      // Navigate to research management page with the specific research highlighted
      setLocation(`/dashboard/herramientas/seo-gpt-optimizer/research-management?highlight=${contentId}`);
    }
  };

  const filteredContents = allProjectContents.filter(content => {
    if (activeTab === 'all') return true;
    if (activeTab === 'blogs') return content.content_type === 'blog' || content.content_type === 'article';
    if (activeTab === 'research') return content.content_type === 'research';
    return true;
  });

  // Show loading state while project or content is loading
  if (loading.isLoading || contentLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Cargando proyecto...</h2>
          <p className="text-gray-600">Obteniendo contenidos y datos del proyecto</p>
        </div>
      </div>
    );
  }

  if (!currentProject) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Proyecto no encontrado</h2>
          <button
            onClick={handleBack}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            ← Volver a proyectos
          </button>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBack}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                    <Folder className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">{currentProject.title}</h1>
                    <p className="text-gray-600 text-sm">{currentProject.topic}</p>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                {/* Botones específicos según la pestaña activa */}
                {activeTab === 'all' && (
                  <>
                    <button
                      onClick={handleCreateResearch}
                      className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-xl transition-colors duration-200"
                    >
                      <Search className="w-4 h-4" />
                      Nuevo Research
                    </button>

                    <button
                      onClick={handleCreateBlog}
                      className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
                    >
                      <Plus className="w-4 h-4" />
                      Nuevo Blog
                    </button>
                  </>
                )}

                {activeTab === 'blogs' && (
                  <button
                    onClick={handleCreateBlog}
                    className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
                  >
                    <Plus className="w-4 h-4" />
                    Nuevo Blog
                  </button>
                )}

                {activeTab === 'research' && (
                  <button
                    onClick={handleCreateResearch}
                    className="flex items-center gap-2 bg-gradient-to-r from-green-600 to-blue-600 text-white px-4 py-2 rounded-xl font-medium hover:from-green-700 hover:to-blue-700 transition-all duration-200"
                  >
                    <Search className="w-4 h-4" />
                    Nuevo Research
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Project Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <motion.div
              className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm font-medium">Total Contenidos</p>
                  <p className="text-2xl font-bold text-gray-900">{allProjectContents.length}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm font-medium">Blogs Publicados</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {allProjectContents.filter(c => c.type === 'blog' && c.status === 'published').length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm font-medium">Research Realizados</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {allProjectContents.filter(c => c.type === 'research').length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                  <Search className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm font-medium">Puntuación Promedio</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {(currentProject.current_gpt_rank_score || 0).toFixed(1)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                  <Target className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
            </motion.div>
          </div>

          {/* Content Tabs */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex">
                  {[
                    { key: 'all', label: 'Todo', count: allProjectContents.length },
                    { key: 'blogs', label: 'Blogs', count: allProjectContents.filter(c => c.content_type === 'blog' || c.content_type === 'article').length },
                    { key: 'research', label: 'Research', count: allProjectContents.filter(c => c.content_type === 'research').length }
                  ].map((tab) => (
                    <button
                      key={tab.key}
                      onClick={() => setActiveTab(tab.key as any)}
                      className={`px-6 py-4 text-sm font-medium transition-colors duration-200 ${
                        activeTab === tab.key
                          ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }`}
                    >
                      {tab.label} ({tab.count})
                    </button>
                  ))}
                </div>

                {/* Refresh Button */}
                <div className="px-6">
                  <button
                    onClick={handleRefreshContent}
                    disabled={contentLoading}
                    className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors duration-200 disabled:opacity-50"
                    title="Actualizar contenido"
                  >
                    <svg className={`w-4 h-4 ${contentLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    <span className="text-sm">Actualizar</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Content List */}
            <div className="p-6">
              {filteredContents.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FileText className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {activeTab === 'all' ? 'No hay contenido aún' :
                     activeTab === 'blogs' ? 'No hay blogs aún' :
                     'No hay research aún'}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {activeTab === 'all' ? 'Comienza creando tu primer blog o research para este proyecto' :
                     activeTab === 'blogs' ? 'Crea tu primer blog para este proyecto' :
                     'Realiza tu primera investigación para este proyecto'}
                  </p>
                  <div className="flex items-center justify-center gap-3">
                    {/* Mostrar botones según la pestaña activa */}
                    {activeTab === 'all' && (
                      <>
                        <button
                          onClick={handleCreateResearch}
                          className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-xl transition-colors duration-200"
                        >
                          <Search className="w-4 h-4" />
                          Crear Research
                        </button>
                        <button
                          onClick={handleCreateBlog}
                          className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
                        >
                          <Plus className="w-4 h-4" />
                          Crear Blog
                        </button>
                      </>
                    )}

                    {activeTab === 'blogs' && (
                      <button
                        onClick={handleCreateBlog}
                        className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
                      >
                        <Plus className="w-4 h-4" />
                        Crear Blog
                      </button>
                    )}

                    {activeTab === 'research' && (
                      <button
                        onClick={handleCreateResearch}
                        className="flex items-center gap-2 bg-gradient-to-r from-green-600 to-blue-600 text-white px-4 py-2 rounded-xl font-medium hover:from-green-700 hover:to-blue-700 transition-all duration-200"
                      >
                        <Search className="w-4 h-4" />
                        Crear Research
                      </button>
                    )}
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredContents.map((content, index) => (
                    <motion.div
                      key={content.content_id}
                      className="border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="flex items-center justify-between">
                        <div
                          className="flex items-center gap-3 cursor-pointer flex-1"
                          onClick={() => handleViewContent(content.content_id, content.content_type)}
                        >
                          <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                            content.content_type === 'blog' || content.content_type === 'article'
                              ? 'bg-blue-100 text-blue-600'
                              : 'bg-purple-100 text-purple-600'
                          }`}>
                            {content.content_type === 'blog' || content.content_type === 'article' ? <FileText className="w-5 h-5" /> : <Search className="w-5 h-5" />}
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">{content.title}</h3>
                            <div className="flex items-center gap-4 mt-1">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                content.status === 'published' ? 'bg-green-100 text-green-700' :
                                content.status === 'completed' ? 'bg-blue-100 text-blue-700' :
                                'bg-gray-100 text-gray-700'
                              }`}>
                                {content.content_type === 'research' ? 'Investigación' : content.status}
                              </span>
                              {(content.content_type === 'blog' || content.content_type === 'article') && (
                                <span className="text-xs text-gray-500">
                                  {content.word_count} palabras
                                </span>
                              )}
                              <span className="text-xs text-gray-500">
                                {content.content_type === 'research'
                                  ? `Confianza: ${content.current_gpt_rank_score.toFixed(0)}%`
                                  : `Score: ${content.current_gpt_rank_score}`
                                }
                              </span>
                              <span className="text-xs text-gray-500">
                                {new Date(content.created_at).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleViewContent(content.content_id, content.content_type)}
                            className="p-2 text-gray-400 hover:text-blue-600 rounded-lg transition-colors duration-200"
                            title="Ver contenido"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button
                            onClick={(e) => handleEditContent(e, content.content_id)}
                            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors duration-200"
                            title="Editar contenido"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={(e) => handleDeleteContent(e, content.content_id)}
                            className="p-2 text-gray-400 hover:text-red-600 rounded-lg transition-colors duration-200"
                            title="Eliminar contenido"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default ProjectDashboardPage;
