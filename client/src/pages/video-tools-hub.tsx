/**
 * Video Tools Hub
 * Página central para acceder a todas las herramientas de video de Emma Studio
 */
import React from "react";
import { <PERSON> } from "wouter";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Video,
  ImageIcon,
  Wand2,
  FileVideo,
  ChevronRight,
} from "lucide-react";

export default function VideoToolsHub() {
  console.log("DEBUG: Renderizando VideoToolsHub v2 - Marca visible");
  return (
    <div className="container max-w-7xl mx-auto px-4 py-6">
      <div className="bg-yellow-300 text-black text-3xl font-extrabold p-4 mb-6 rounded-lg text-center shadow-lg">
        DEBUG: ESTA ES LA VERSIÓN EDITADA DE VideoToolsHub (marca visible)
      </div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Herramientas de Video
          </h1>
          <p className="text-gray-500 mt-2">
            Crea, edita y personaliza videos con nuestras herramientas de IA.
          </p>
        </div>

        <div className="flex gap-3">
          <Link href="/emma-studio">
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Regresar
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
        {/* Editor de Video */}
        <VideoToolCard
          title="Editor de Video AI"
          description="Editor completo para crear y editar videos con asistencia de IA"
          icon={<Video className="w-10 h-10 text-red-600" />}
          linkTo="/video-editor"
          linkText="Abrir Editor"
        />

        {/* Movimiento sútil */}
        <VideoToolCard
          title="Movimiento sútil"
          description="Convierte cualquier imagen en un video animado con movimiento realista usando Stable Video Diffusion"
          icon={<ImageIcon className="w-10 h-10 text-blue-600" />}
          linkTo="/movement"
          linkText="Convertir Imagen"
          isHighlighted={true}
        />

        {/* Generador Simple de Video */}
        <VideoToolCard
          title="Generador Simple"
          description="Crea videos rápidos a partir de prompts de texto mediante inteligencia artificial"
          icon={<Wand2 className="w-10 h-10 text-green-600" />}
          linkTo="/simple-video-generator"
          linkText="Generar Video"
        />

        {/* Generador Automático de Video */}
        <VideoToolCard
          title="Generador Automático"
          description="Genera videos completos desde cero con múltiples clips, texto y audio"
          icon={<FileVideo className="w-10 h-10 text-purple-600" />}
          linkTo="/video-generator-old"
          linkText="Crear Video Completo"
        />

        {/* Generador Automático de Video - NUEVA HERRAMIENTA */}
        <VideoToolCard
          title="Generador Automático (Nuevo)"
          description="Genera videos completos con IA avanzada, múltiples clips, texto, audio y efectos. Basado en la arquitectura moderna de Emma Studio."
          icon={<FileVideo className="w-10 h-10 text-purple-700" />}
          linkTo="/video-generator"
          linkText="Crear Video IA"
          isHighlighted={true}
        />
      </div>
    </div>
  );
}

interface VideoToolCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  linkTo: string;
  linkText: string;
  isHighlighted?: boolean;
}

function VideoToolCard({
  title,
  description,
  icon,
  linkTo,
  linkText,
  isHighlighted = false,
}: VideoToolCardProps) {
  return (
    <Card
      className={`overflow-hidden transition-all duration-300 hover:-translate-y-1 ${
        isHighlighted
          ? "border-2 border-blue-400 shadow-[0_0_0_2px_rgba(59,130,246,0.3)]"
          : "border hover:shadow-lg"
      }`}
    >
      <CardHeader className={`pb-2 ${isHighlighted ? "bg-blue-50" : ""}`}>
        <div className="flex items-center gap-3">
          {icon}
          <CardTitle className="text-xl">{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="pt-4">
        <CardDescription className="text-sm text-gray-600 min-h-[60px]">
          {description}
        </CardDescription>
      </CardContent>
      <CardFooter className="bg-gray-50 pt-3 pb-3 flex justify-end">
        <Link href={linkTo}>
          <Button className="gap-1">
            {linkText}
            <ChevronRight className="w-4 h-4" />
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
